/**
 * NavSphere导航页主应用
 * 整合所有模块，实现完整的导航页功能
 */
class NavApp {
    constructor() {
        this.data = null;
        this.flatCategories = [];
        this.allSites = [];
        this.currentCategory = 'all-categories'; // 默认显示全部分类
        this.isLoading = false;
        this.isShowingAllCategories = true; // 标记是否显示所有分类
        this.lastActiveCategoryId = null; // 记录上次活动的分类ID
        this.scrollListener = null; // 滚动监听器

        // 初始化访问记录管理器
        this.visitManager = new VisitManager();

        // 视图模式管理
        this.isCompactMode = false; // 默认为标准模式
        this.viewToggleBtn = null;
        this.viewToggleIcon = null;
        this.viewModeText = null;

        // 管理器实例
        this.themeManager = null;
        this.searchManager = null;
        this.sidebarManager = null;
        this.timeNotificationManager = null;

        // DOM元素
        this.sitesContainer = null;
        this.currentCategoryTitle = null;
        this.sitesCount = null;
        this.loadingSpinner = null;
        this.emptyState = null;

        // 卡片快捷键功能
        this.hoveredCard = null; // 当前悬停的卡片
        this.cardKeyboardListener = null; // 卡片键盘事件监听器

        this.init();
    }
    
    /**
     * 初始化应用
     */
    async init() {
        try {
            console.log('NavSphere应用初始化开始...');
            console.log('当前设备信息:', {
                userAgent: navigator.userAgent,
                screenWidth: window.screen.width,
                screenHeight: window.screen.height,
                windowWidth: window.innerWidth,
                windowHeight: window.innerHeight,
                isMobile: window.innerWidth <= 767
            });

            // 等待DOM加载完成
            if (document.readyState === 'loading') {
                console.log('等待DOM加载完成...');
                await new Promise(resolve => {
                    document.addEventListener('DOMContentLoaded', resolve);
                });
            }
            console.log('DOM已加载完成');

            // 初始化DOM元素
            this.initElements();

            // 初始化管理器
            this.initManagers();

            // 加载数据
            await this.loadData();

            // 渲染界面
            this.render();

            // 确保状态同步
            this.ensureCorrectState();

        // 绑定事件
        this.bindEvents();

        // 初始化视图模式
        this.initViewMode();

        // 初始化数据管理UI
        this.initDataManagementUI();

        // 启动自动备份机制
        this.startAutoBackup();

        // 初始化页面关闭前备份
        this.initBeforeUnloadBackup();

        // 确保键盘事件监听器已绑定（修复空格键预览BUG）
        this.bindCardKeyboardEvents();

            console.log('NavSphere应用初始化完成！');

        } catch (error) {
            console.error('应用初始化失败:', error);
            this.showErrorMessage('应用初始化失败，请刷新页面重试');
        }
    }
    
    /**
     * 初始化DOM元素
     */
    initElements() {
        console.log('初始化DOM元素...');

        this.sitesContainer = document.getElementById('sitesContainer');
        this.currentCategoryTitle = document.getElementById('currentCategoryTitle');
        this.sitesCount = document.getElementById('sitesCount');
        this.loadingSpinner = document.getElementById('loadingSpinner');
        this.emptyState = document.getElementById('emptyState');

        // 视图切换相关元素
        this.viewToggleBtn = document.getElementById('viewToggleBtn');
        this.viewToggleIcon = document.getElementById('viewToggleIcon');
        this.viewModeText = document.querySelector('.view-mode-text');

        console.log('DOM元素检查:', {
            sitesContainer: !!this.sitesContainer,
            currentCategoryTitle: !!this.currentCategoryTitle,
            sitesCount: !!this.sitesCount,
            loadingSpinner: !!this.loadingSpinner,
            emptyState: !!this.emptyState,
            viewToggleBtn: !!this.viewToggleBtn,
            viewToggleIcon: !!this.viewToggleIcon,
            viewModeText: !!this.viewModeText
        });

        if (!this.sitesContainer || !this.currentCategoryTitle) {
            const missingElements = [];
            if (!this.sitesContainer) missingElements.push('sitesContainer');
            if (!this.currentCategoryTitle) missingElements.push('currentCategoryTitle');

            console.error('缺少必要的DOM元素:', missingElements);
            throw new Error(`必要的DOM元素未找到: ${missingElements.join(', ')}`);
        }

        // 确保内容页标题初始状态为显示（默认不是全部分类模式）
        const contentHeader = document.querySelector('.content-header');
        if (contentHeader) {
            contentHeader.style.display = 'flex';
        }

        console.log('DOM元素初始化完成');
    }
    
    /**
     * 初始化管理器
     */
    initManagers() {
        // 使用全局主题管理器实例，避免重复创建
        this.themeManager = window.themeManager;
        
        if (!this.themeManager) {
            console.warn('全局主题管理器尚未初始化，将在稍后初始化');
            // 等待主题管理器初始化
            const waitForThemeManager = () => {
                if (window.themeManager) {
                    this.themeManager = window.themeManager;
                    console.log('主题管理器已连接到NavApp');
                } else {
                    setTimeout(waitForThemeManager, 100);
                }
            };
            waitForThemeManager();
        }
        
        // 搜索管理器
        this.searchManager = new SearchManager(this);
        
        // 侧边栏管理器
        this.sidebarManager = new SidebarManager(this);
        
        // Markdown 管理器
        this.markdownManager = new MarkdownManager();

        // 时间范围提示管理器
        if (typeof TimeRangeNotificationManager !== 'undefined') {
            this.timeNotificationManager = new TimeRangeNotificationManager();
        }

        // 同步当前分类状态
        this.syncCurrentCategory();
        
        console.log('所有管理器初始化完成');
    }
    
    /**
     * 同步当前分类状态
     */
    syncCurrentCategory() {
        if (this.sidebarManager) {
            // 从侧边栏获取保存的分类状态（因为侧边栏在loadExpandedState中已经加载了localStorage）
            const sidebarCategory = this.sidebarManager.getCurrentCategory();
            if (sidebarCategory !== this.currentCategory) {
                console.log(`同步分类状态: ${this.currentCategory} -> ${sidebarCategory}`);
                this.currentCategory = sidebarCategory;
                // 更新显示模式
                this.isShowingAllCategories = (sidebarCategory === 'all-categories');
            }
        }
    }

    /**
     * 确保应用状态正确
     */
    ensureCorrectState() {
        console.log('确保应用状态正确...');

        // 确保当前分类和显示模式一致
        if (this.currentCategory === 'all-categories') {
            this.isShowingAllCategories = true;
            console.log('设置为显示所有分类模式');
        } else {
            this.isShowingAllCategories = false;
            console.log('设置为单分类模式:', this.currentCategory);
        }

        // 确保侧边栏状态正确（状态已经在syncCurrentCategory中同步）
        if (this.sidebarManager) {
            // 确保侧边栏的currentCategory与主应用一致
            this.sidebarManager.currentCategory = this.currentCategory;
        }

        // 确保标题正确
        this.updateCorrectHeader();

        console.log('应用状态确保完成:', {
            currentCategory: this.currentCategory,
            isShowingAllCategories: this.isShowingAllCategories
        });
    }

    /**
     * 更新正确的标题
     */
    updateCorrectHeader() {
        if (this.currentCategory === 'all-categories') {
            this.updateHeaderForAllCategories();
        } else {
            const category = this.getCategoryById(this.currentCategory);
            const sites = this.getSitesByCategory(this.currentCategory);
            this.updateHeader(category, sites);
        }
    }
    
    /**
     * 加载数据
     */
    async loadData() {
        try {
            this.showLoading(true);

            console.log('开始加载网站数据...');

            // 尝试加载配置文件
            let config = null;
            try {
                const configResponse = await fetch('./data/appconfig.json');
                if (configResponse.ok) {
                    config = await configResponse.json();
                    console.log('配置文件加载成功:', config);
                }
            } catch (configError) {
                console.log('配置文件不存在或加载失败，使用默认配置:', configError.message);
            }

            // 根据配置加载数据
            if (config && config.dataSources && config.dataSources.length > 0) {
                this.data = await this.loadMultipleDataSources(config);
                this.dataSourcesInfo = config.dataSources.filter(s => s.enabled);
            } else {
                // 降级到原有的单文件加载方式
                this.data = await this.loadSingleDataSource('./data/sites.json');
                this.dataSourcesInfo = [{
                    id: 'default',
                    name: '默认数据源',
                    path: './data/sites.json',
                    enabled: true,
                    priority: 1
                }];
            }

            // 记录加载时间
            this.dataLoadedAt = new Date().toISOString();

            // 处理数据
            this.processData();

            console.log(`数据加载完成: ${this.data.categories.length} 个分类, ${this.allSites.length} 个网站`);

        } catch (error) {
            console.error('数据加载失败:', error);
            throw new Error('无法加载网站数据，请检查网络连接');
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * 加载单个数据源（原有逻辑）
     * @param {string} path 数据文件路径
     * @returns {Object} 网站数据
     */
    async loadSingleDataSource(path) {
        console.log(`加载单个数据源: ${path}`);
        const response = await fetch(path);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return await response.json();
    }

    /**
     * 加载多个数据源并合并
     * @param {Object} config 配置对象
     * @returns {Object} 合并后的网站数据
     */
    async loadMultipleDataSources(config) {
        console.log('开始加载多个数据源...');

        // 筛选启用的数据源并按优先级排序
        const enabledSources = config.dataSources
            .filter(source => source.enabled)
            .sort((a, b) => a.priority - b.priority);

        console.log(`找到 ${enabledSources.length} 个启用的数据源:`, enabledSources.map(s => s.name));

        const dataSources = [];
        const loadErrors = [];

        // 并行加载所有数据源
        for (const source of enabledSources) {
            try {
                console.log(`正在加载数据源: ${source.name} (${source.path})`);
                const data = await this.loadSingleDataSource(source.path);
                dataSources.push({
                    ...source,
                    data: data
                });
                console.log(`数据源 ${source.name} 加载成功`);
            } catch (error) {
                console.error(`数据源 ${source.name} 加载失败:`, error);
                loadErrors.push({
                    source: source,
                    error: error
                });
            }
        }

        // 检查是否有成功加载的数据源
        if (dataSources.length === 0) {
            console.error('所有数据源加载失败，尝试降级处理');

            // 尝试多种降级方案
            const fallbackPaths = [
                config.fallback?.defaultPath,
                './data/sites.json',
                './data/main-sites.json'
            ].filter(Boolean);

            for (const fallbackPath of fallbackPaths) {
                try {
                    console.log(`尝试降级路径: ${fallbackPath}`);
                    const fallbackData = await this.loadSingleDataSource(fallbackPath);
                    console.log(`降级成功: ${fallbackPath}`);
                    return fallbackData;
                } catch (fallbackError) {
                    console.warn(`降级路径失败: ${fallbackPath}`, fallbackError.message);
                }
            }

            // 所有降级方案都失败
            throw new Error(`所有数据源和降级方案都加载失败。错误详情: ${loadErrors.map(e => `${e.source.name}: ${e.error.message}`).join('; ')}`);
        }

        // 如果有部分数据源加载失败，记录警告但继续处理
        if (loadErrors.length > 0) {
            console.warn(`${loadErrors.length} 个数据源加载失败:`, loadErrors.map(e => e.source.name));
            // 可以选择显示用户提示
            if (typeof showToast === 'function') {
                showToast(`部分数据源加载失败: ${loadErrors.map(e => e.source.name).join(', ')}`, 'warning', 5000);
            }
        }

        // 合并数据源
        console.log(`开始合并 ${dataSources.length} 个数据源...`);
        const mergedData = this.mergeDataSources(dataSources, config.mergeStrategy || {});

        // 验证合并后的数据
        if (config.validation) {
            console.log('开始验证合并后的数据...');
            const validationResult = this.validateDataStructure(mergedData, config.validation);

            if (!validationResult.isValid) {
                console.error('数据验证失败:', validationResult.errors);
                if (config.validation.strictMode) {
                    throw new Error(`数据验证失败: ${validationResult.errors.join('; ')}`);
                }
            }

            if (validationResult.warnings.length > 0) {
                console.warn('数据验证警告:', validationResult.warnings);
            }

            console.log('数据验证统计:', validationResult.stats);
        }

        console.log('数据源合并完成');
        return mergedData;
    }

    /**
     * 合并多个数据源
     * @param {Array} dataSources 数据源数组
     * @param {Object} mergeStrategy 合并策略
     * @returns {Object} 合并后的数据
     */
    mergeDataSources(dataSources, mergeStrategy = {}) {
        console.log('开始合并数据源，策略:', mergeStrategy);

        // 默认合并策略
        const strategy = {
            duplicateHandling: 'merge',
            categoryMerging: 'append',
            siteIdConflict: 'keepFirst',
            preserveOrder: true,
            ...mergeStrategy
        };

        // 初始化合并结果
        const mergedData = {
            categories: []
        };

        // 用于跟踪已处理的分类和网站ID
        const processedCategoryIds = new Set();
        const processedSiteIds = new Set();
        const categoryMap = new Map(); // 用于快速查找分类

        // 按优先级顺序处理每个数据源
        for (const source of dataSources) {
            console.log(`处理数据源: ${source.name}`);

            if (!source.data || !source.data.categories) {
                console.warn(`数据源 ${source.name} 格式无效，跳过`);
                continue;
            }

            // 处理分类数据
            this.mergeCategoriesFromSource(
                source.data.categories,
                mergedData.categories,
                processedCategoryIds,
                processedSiteIds,
                categoryMap,
                strategy,
                source.name
            );
        }

        console.log(`合并完成: ${mergedData.categories.length} 个顶级分类`);
        return mergedData;
    }

    /**
     * 从单个数据源合并分类数据
     * @param {Array} sourceCategories 源分类数组
     * @param {Array} targetCategories 目标分类数组
     * @param {Set} processedCategoryIds 已处理的分类ID集合
     * @param {Set} processedSiteIds 已处理的网站ID集合
     * @param {Map} categoryMap 分类映射表
     * @param {Object} strategy 合并策略
     * @param {string} sourceName 数据源名称
     */
    mergeCategoriesFromSource(sourceCategories, targetCategories, processedCategoryIds, processedSiteIds, categoryMap, strategy, sourceName) {
        for (const category of sourceCategories) {
            this.mergeSingleCategory(
                category,
                targetCategories,
                processedCategoryIds,
                processedSiteIds,
                categoryMap,
                strategy,
                sourceName,
                null // parentId
            );
        }
    }

    /**
     * 合并单个分类
     * @param {Object} sourceCategory 源分类
     * @param {Array} targetCategories 目标分类数组
     * @param {Set} processedCategoryIds 已处理的分类ID集合
     * @param {Set} processedSiteIds 已处理的网站ID集合
     * @param {Map} categoryMap 分类映射表
     * @param {Object} strategy 合并策略
     * @param {string} sourceName 数据源名称
     * @param {string} parentId 父分类ID
     */
    mergeSingleCategory(sourceCategory, targetCategories, processedCategoryIds, processedSiteIds, categoryMap, strategy, sourceName, parentId) {
        const categoryId = sourceCategory.id;

        if (processedCategoryIds.has(categoryId)) {
            // 分类ID已存在，根据策略处理
            console.log(`分类ID冲突: ${categoryId} (来源: ${sourceName})`);

            if (strategy.categoryMerging === 'append') {
                // 将内容追加到现有分类
                const existingCategory = categoryMap.get(categoryId);
                if (existingCategory) {
                    this.appendCategoryContent(existingCategory, sourceCategory, processedSiteIds, strategy, sourceName);
                }
            } else if (strategy.categoryMerging === 'merge') {
                // 深度合并分类内容
                const existingCategory = categoryMap.get(categoryId);
                if (existingCategory) {
                    this.deepMergeCategoryContent(existingCategory, sourceCategory, processedSiteIds, strategy, sourceName);
                }
            }
            // 'skip' 策略：跳过重复分类
            return;
        }

        // 创建新分类
        const newCategory = {
            id: categoryId,
            name: sourceCategory.name,
            icon: sourceCategory.icon,
            sites: [],
            children: []
        };

        // 处理网站数据
        if (sourceCategory.sites && sourceCategory.sites.length > 0) {
            for (const site of sourceCategory.sites) {
                this.mergeSingleSite(site, newCategory.sites, processedSiteIds, strategy, sourceName);
            }
        }

        // 处理子分类
        if (sourceCategory.children && sourceCategory.children.length > 0) {
            for (const childCategory of sourceCategory.children) {
                this.mergeSingleCategory(
                    childCategory,
                    newCategory.children,
                    processedCategoryIds,
                    processedSiteIds,
                    categoryMap,
                    strategy,
                    sourceName,
                    categoryId
                );
            }
        }

        // 添加到目标数组和映射表
        targetCategories.push(newCategory);
        categoryMap.set(categoryId, newCategory);
        processedCategoryIds.add(categoryId);

        console.log(`添加分类: ${newCategory.name} (${newCategory.sites.length} 个网站, ${newCategory.children.length} 个子分类)`);
    }

    /**
     * 合并单个网站
     * @param {Object} sourceSite 源网站
     * @param {Array} targetSites 目标网站数组
     * @param {Set} processedSiteIds 已处理的网站ID集合
     * @param {Object} strategy 合并策略
     * @param {string} sourceName 数据源名称
     */
    mergeSingleSite(sourceSite, targetSites, processedSiteIds, strategy, sourceName) {
        const siteId = sourceSite.id;

        if (processedSiteIds.has(siteId)) {
            console.log(`网站ID冲突: ${siteId} (来源: ${sourceName})`);

            if (strategy.siteIdConflict === 'keepLast') {
                // 替换现有网站
                const existingIndex = targetSites.findIndex(site => site.id === siteId);
                if (existingIndex !== -1) {
                    targetSites[existingIndex] = { ...sourceSite };
                    console.log(`替换网站: ${sourceSite.name}`);
                }
            } else if (strategy.siteIdConflict === 'merge') {
                // 合并网站信息
                const existingIndex = targetSites.findIndex(site => site.id === siteId);
                if (existingIndex !== -1) {
                    const existingSite = targetSites[existingIndex];
                    targetSites[existingIndex] = this.mergeSiteContent(existingSite, sourceSite);
                    console.log(`合并网站: ${sourceSite.name}`);
                }
            }
            // 'keepFirst' 策略：跳过重复网站
            return;
        }

        // 添加新网站
        targetSites.push({ ...sourceSite });
        processedSiteIds.add(siteId);
        console.log(`添加网站: ${sourceSite.name}`);
    }

    /**
     * 追加分类内容
     * @param {Object} existingCategory 现有分类
     * @param {Object} sourceCategory 源分类
     * @param {Set} processedSiteIds 已处理的网站ID集合
     * @param {Object} strategy 合并策略
     * @param {string} sourceName 数据源名称
     */
    appendCategoryContent(existingCategory, sourceCategory, processedSiteIds, strategy, sourceName) {
        console.log(`追加内容到分类: ${existingCategory.name}`);

        // 追加网站
        if (sourceCategory.sites && sourceCategory.sites.length > 0) {
            for (const site of sourceCategory.sites) {
                this.mergeSingleSite(site, existingCategory.sites, processedSiteIds, strategy, sourceName);
            }
        }

        // 追加子分类（这里需要递归处理，但为了简化先跳过）
        if (sourceCategory.children && sourceCategory.children.length > 0) {
            console.log(`跳过子分类追加，需要递归处理: ${sourceCategory.children.length} 个子分类`);
        }
    }

    /**
     * 深度合并分类内容
     * @param {Object} existingCategory 现有分类
     * @param {Object} sourceCategory 源分类
     * @param {Set} processedSiteIds 已处理的网站ID集合
     * @param {Object} strategy 合并策略
     * @param {string} sourceName 数据源名称
     */
    deepMergeCategoryContent(existingCategory, sourceCategory, processedSiteIds, strategy, sourceName) {
        console.log(`深度合并分类: ${existingCategory.name}`);

        // 合并基本信息（保留现有的，只补充缺失的）
        if (!existingCategory.icon && sourceCategory.icon) {
            existingCategory.icon = sourceCategory.icon;
        }

        // 合并网站
        if (sourceCategory.sites && sourceCategory.sites.length > 0) {
            for (const site of sourceCategory.sites) {
                this.mergeSingleSite(site, existingCategory.sites, processedSiteIds, strategy, sourceName);
            }
        }

        // 深度合并子分类（递归处理）
        if (sourceCategory.children && sourceCategory.children.length > 0) {
            console.log(`深度合并子分类: ${sourceCategory.children.length} 个子分类`);
            // 这里需要更复杂的递归逻辑，暂时简化处理
        }
    }

    /**
     * 合并网站内容
     * @param {Object} existingSite 现有网站
     * @param {Object} sourceSite 源网站
     * @returns {Object} 合并后的网站
     */
    mergeSiteContent(existingSite, sourceSite) {
        const merged = { ...existingSite };

        // 合并标签
        if (sourceSite.tags && sourceSite.tags.length > 0) {
            const existingTags = new Set(merged.tags || []);
            for (const tag of sourceSite.tags) {
                existingTags.add(tag);
            }
            merged.tags = Array.from(existingTags);
        }

        // 补充缺失的字段
        if (!merged.description && sourceSite.description) {
            merged.description = sourceSite.description;
        }
        if (!merged.icon && sourceSite.icon) {
            merged.icon = sourceSite.icon;
        }

        return merged;
    }

    /**
     * 验证数据结构
     * @param {Object} data 要验证的数据
     * @param {Object} validationConfig 验证配置
     * @returns {Object} 验证结果
     */
    validateDataStructure(data, validationConfig = {}) {
        const validation = {
            isValid: true,
            errors: [],
            warnings: [],
            stats: {
                totalCategories: 0,
                totalSites: 0,
                duplicateIds: []
            }
        };

        if (!data || !data.categories) {
            validation.isValid = false;
            validation.errors.push('数据结构无效：缺少 categories 字段');
            return validation;
        }

        const seenCategoryIds = new Set();
        const seenSiteIds = new Set();

        // 递归验证分类
        const validateCategories = (categories, level = 0) => {
            for (const category of categories) {
                validation.stats.totalCategories++;

                // 验证分类基本字段
                if (!category.id) {
                    validation.errors.push(`分类缺少ID字段: ${category.name || '未知分类'}`);
                    validation.isValid = false;
                }

                if (!category.name) {
                    validation.warnings.push(`分类缺少名称: ${category.id}`);
                }

                // 检查分类ID重复
                if (category.id && seenCategoryIds.has(category.id)) {
                    validation.stats.duplicateIds.push(`分类ID重复: ${category.id}`);
                    if (validationConfig.requireUniqueIds) {
                        validation.isValid = false;
                        validation.errors.push(`分类ID重复: ${category.id}`);
                    } else {
                        validation.warnings.push(`分类ID重复: ${category.id}`);
                    }
                } else if (category.id) {
                    seenCategoryIds.add(category.id);
                }

                // 验证网站
                if (category.sites && category.sites.length > 0) {
                    for (const site of category.sites) {
                        validation.stats.totalSites++;

                        if (!site.id) {
                            validation.errors.push(`网站缺少ID字段: ${site.name || '未知网站'} (分类: ${category.name})`);
                            validation.isValid = false;
                        }

                        if (!site.name) {
                            validation.warnings.push(`网站缺少名称: ${site.id} (分类: ${category.name})`);
                        }

                        if (!site.url && !site.markdownFile) {
                            validation.warnings.push(`网站缺少URL或Markdown文件: ${site.name} (分类: ${category.name})`);
                        }

                        // 检查网站ID重复
                        if (site.id && seenSiteIds.has(site.id)) {
                            validation.stats.duplicateIds.push(`网站ID重复: ${site.id}`);
                            if (validationConfig.requireUniqueIds) {
                                validation.isValid = false;
                                validation.errors.push(`网站ID重复: ${site.id}`);
                            } else {
                                validation.warnings.push(`网站ID重复: ${site.id}`);
                            }
                        } else if (site.id) {
                            seenSiteIds.add(site.id);
                        }
                    }
                }

                // 递归验证子分类
                if (category.children && category.children.length > 0) {
                    validateCategories(category.children, level + 1);
                }
            }
        };

        validateCategories(data.categories);

        console.log('数据验证完成:', validation);
        return validation;
    }

    /**
     * 获取数据源信息（调试用）
     * @returns {Object} 数据源信息
     */
    getDataSourceInfo() {
        return {
            hasMultipleDataSources: this.dataSourcesInfo ? this.dataSourcesInfo.length > 1 : false,
            dataSources: this.dataSourcesInfo || [],
            totalCategories: this.flatCategories.length,
            totalSites: this.allSites.length,
            loadedAt: this.dataLoadedAt || null
        };
    }

    /**
     * 重新加载数据源配置
     */
    async reloadDataSources() {
        console.log('重新加载数据源配置...');
        try {
            await this.loadData();
            this.render();
            console.log('数据源重新加载完成');
            if (typeof showToast === 'function') {
                showToast('数据源重新加载成功', 'success');
            }
        } catch (error) {
            console.error('数据源重新加载失败:', error);
            if (typeof showToast === 'function') {
                showToast('数据源重新加载失败', 'error');
            }
        }
    }
    
    /**
     * 处理数据
     */
    processData() {
        if (!this.data || !this.data.categories) {
            console.error('数据格式错误:', this.data);
            throw new Error('数据格式错误');
        }

        // 扁平化分类数据
        this.flatCategories = flattenCategories(this.data.categories);
        console.log('扁平化分类数据:', this.flatCategories);

        // 提取所有网站数据
        this.allSites = getAllSites(this.data.categories);
        console.log('所有网站数据:', this.allSites);

        // 验证默认分类是否存在
        // 注意：'all-categories' 和 'frequent' 是特殊的虚拟分类，不在 flatCategories 中
        if (this.currentCategory !== 'all-categories' && this.currentCategory !== 'frequent') {
            const defaultCategory = this.flatCategories.find(cat => cat.id === this.currentCategory);
            if (!defaultCategory) {
                console.warn(`默认分类 ${this.currentCategory} 不存在，使用智能默认分类`);
                this.currentCategory = this.getSmartDefaultCategory();
                this.isShowingAllCategories = (this.currentCategory === 'all-categories');
                console.log('设置默认分类为:', this.currentCategory);
            } else {
                this.isShowingAllCategories = false;
            }
        } else if (this.currentCategory === 'all-categories') {
            this.isShowingAllCategories = true;
            console.log('使用所有分类模式');
        } else {
            // 常用分类
            this.isShowingAllCategories = false;
            console.log('使用常用分类模式');
        }

        // 更新搜索数据
        if (this.searchManager) {
            this.searchManager.updateSearchData(this.allSites);
            // 重新初始化筛选器以包含新数据
            this.searchManager.initializeFilters();
        }

        // 重新同步分类状态
        this.syncCurrentCategory();

        console.log('数据处理完成，当前分类:', this.currentCategory, '显示所有分类:', this.isShowingAllCategories);
    }
    
    /**
     * 渲染界面
     */
    render() {
        try {
            console.log('开始渲染界面，当前分类:', this.currentCategory, '显示所有分类:', this.isShowingAllCategories);

            // 渲染侧边栏
            this.renderSidebar();

            // 渲染网站列表
            this.renderSites();

            console.log('界面渲染完成');

        } catch (error) {
            console.error('界面渲染失败:', error);
            this.showErrorMessage('界面渲染失败');
        }
    }
    
    /**
     * 渲染侧边栏
     */
    renderSidebar() {
        if (this.sidebarManager && this.data && this.data.categories) {
            this.sidebarManager.renderCategories(this.data.categories);
            // 分类状态已经在syncCurrentCategory中同步，不需要额外设置
        }
    }
    
    /**
     * 渲染网站列表
     */
    renderSites(categoryId = this.currentCategory) {
        if (!this.sitesContainer) {
            console.error('sitesContainer 元素未找到');
            return;
        }

        try {
            // 检查是否显示所有分类
            if (categoryId === 'all-categories') {
                this.renderAllCategoriesSites();
                return;
            }

            // 验证并修正分类ID（但对于常用分类，不进行智能重定向）
            const validCategoryId = this.validateAndFixCategory(categoryId);
            if (validCategoryId !== categoryId) {
                console.log(`分类ID已修正: ${categoryId} -> ${validCategoryId}`);
                categoryId = validCategoryId;
                this.currentCategory = validCategoryId;
            }

            console.log(`渲染分类 ${categoryId} 的网站列表`);
            const sites = this.getSitesByCategory(categoryId);
            const category = this.getCategoryById(categoryId);

            console.log('找到的网站:', sites);
            console.log('分类信息:', category);

            // 更新标题和计数
            this.updateHeader(category, sites);

            if (sites.length === 0) {
                console.log('没有找到网站，显示空状态');
                this.showEmptyState();
                return;
            }

            // 检查是否是父分类（有子分类且没有直接站点）
            const isParentCategory = category &&
                                   this.hasChildCategories(category.id) &&
                                   (!category.sites || category.sites.length === 0);

            let sitesHtml = '';
            if (isParentCategory) {
                // 按子分类分组显示
                sitesHtml = this.renderGroupedSites(categoryId);
                // 添加分组布局类，移除其他布局类
                this.sitesContainer.classList.add('grouped-layout');
                this.sitesContainer.classList.remove('waterfall-layout');
                console.log('使用分组布局 (grouped-layout)');
            } else {
                // 正常显示网站卡片
                sitesHtml = sites.map(site => this.createSiteCard(site)).join('');
                // 移除所有特殊布局类，使用默认网格布局
                this.sitesContainer.classList.remove('grouped-layout', 'waterfall-layout');
                console.log('使用默认网格布局，移除所有特殊布局类');
            }

            this.sitesContainer.innerHTML = sitesHtml;

            // 绑定卡片事件
            this.bindSiteCardEvents();

            // 隐藏空状态
            this.hideEmptyState();

        } catch (error) {
            console.error('渲染网站列表失败:', error);
            this.showErrorMessage('无法显示网站列表');
        }
    }
    
    /**
     * 渲染所有分类的网站（瀑布流模式，平铺显示所有有网站的分类）
     */
    renderAllCategoriesSites() {
        console.log('渲染所有分类的网站（平铺模式）');

        this.isShowingAllCategories = true;
        this.currentCategory = 'all-categories';

        // 更新标题
        this.updateHeaderForAllCategories();

        // 获取原始分类数据
        const categories = this.data.categories;

        let allSitesHtml = '';
        let totalSitesCount = 0;



        // 按照原始分类顺序渲染，保持分类的逻辑顺序
        const renderCategoryAndChildren = (category, isTopLevel = false) => {
            // 为没有直接网站的顶级分类添加占位符（用于滚动定位）
            if (isTopLevel && (!category.sites || category.sites.length === 0) &&
                category.children && category.children.length > 0) {
                allSitesHtml += `<div class="category-section" data-category-id="${category.id}" style="margin-bottom: 0;"></div>`;
            }

            // 如果当前分类有网站，渲染它
            if (category.sites && category.sites.length > 0) {
                allSitesHtml += this.renderCategorySection(category, category.sites, 0);
                totalSitesCount += category.sites.length;
            }

            // 递归渲染子分类
            if (category.children && category.children.length > 0) {
                category.children.forEach(child => {
                    renderCategoryAndChildren(child, false);
                });
            }
        };

        // 渲染所有顶级分类
        categories.forEach(category => {
            renderCategoryAndChildren(category, true);
        });

        // 添加瀑布流布局类
        this.sitesContainer.classList.add('waterfall-layout');
        this.sitesContainer.classList.remove('grouped-layout');
        console.log('使用瀑布流布局 (waterfall-layout)');

        this.sitesContainer.innerHTML = allSitesHtml;

        // 绑定卡片事件
        this.bindSiteCardEvents();

        // 初始化滚动监听
        this.initScrollListener();

        // 隐藏空状态
        this.hideEmptyState();

        console.log(`所有分类渲染完成，共 ${totalSitesCount} 个网站`);
    }

    /**
     * 渲染单个分类区域
     * @param {Object} category 分类数据
     * @param {Array} sites 网站列表
     * @param {number} level 分类层级（0为顶级）
     * @returns {string} 分类区域HTML
     */
    renderCategorySection(category, sites, level = 0) {
        const levelClass = level > 0 ? `category-section-level-${level}` : '';
        const indentClass = level > 0 ? `category-indent-${level}` : '';

        // 使用精简的标题样式，类似于二级选项卡，但保持层级缩进
        return `
            <div class="category-section ${levelClass} ${indentClass}" data-category-id="${category.id}">
                <div class="category-group-header">
                    <h3 class="category-group-title">
                        <span class="category-section-icon">${category.icon || '📁'}</span>
                        ${category.name}
                    </h3>
                    <span class="category-group-count">${sites.length} 个网站</span>
                </div>
                <div class="category-group-sites">
                    ${sites.map(site => this.createSiteCard(site)).join('')}
                </div>
            </div>
        `;
    }

    /**
     * 按子分类分组渲染网站
     * @param {string} parentCategoryId 父分类ID
     * @returns {string} 分组HTML
     */
    renderGroupedSites(parentCategoryId) {
        const childCategories = this.flatCategories.filter(cat => cat.parentId === parentCategoryId);
        let groupedHtml = '';

        childCategories.forEach(childCategory => {
            const childSites = this.getSitesByCategory(childCategory.id);
            if (childSites.length > 0) {
                groupedHtml += `
                    <div class="category-group">
                        <div class="category-group-header">
                            <h3 class="category-group-title">${childCategory.name}</h3>
                            <span class="category-group-count">${childSites.length} 个网站</span>
                        </div>
                        <div class="category-group-sites">
                            ${childSites.map(site => this.createSiteCard(site)).join('')}
                        </div>
                    </div>
                `;
            }
        });

        return groupedHtml;
    }
    
    /**
     * 创建网站卡片HTML
     * @param {Object} site 网站数据
     * @returns {string} 卡片HTML
     */
    createSiteCard(site) {
        const domain = site.url ? extractDomain(site.url) : '';
        const tags = site.tags ? site.tags.slice(0, 3) : []; // 只显示前3个标签
        
        // 判断卡片类型和设置数据属性
        const hasUrl = site.url && site.url.trim() !== '';
        const hasMarkdown = site.markdownFile && site.markdownFile.trim() !== '';
        
        // 构建数据属性 - 确保所有必要的属性都被设置
        let cardDataAttrs = `data-site-id="${site.id || ''}" data-site-name="${site.name || ''}"`;
        
        // 添加URL数据属性（如果存在）
        if (hasUrl) {
            cardDataAttrs += ` data-url="${site.url}"`;
        }
        
        // 添加Markdown文件数据属性（如果存在）
        if (hasMarkdown) {
            cardDataAttrs += ` data-markdown-file="${site.markdownFile}"`;
        }
        
        // 设置显示内容和样式类
        let urlDisplay = domain;
        let cardTypeClass = '';
        
        if (hasMarkdown && hasUrl) {
            // 情况三：既有网址又有markdown文档
            urlDisplay = domain;
            cardTypeClass = 'site-card-both';
        } else if (hasMarkdown) {
            // 情况二：仅有markdown文档
            urlDisplay = '📄 本地文档';
            cardTypeClass = 'site-card-markdown';
        } else if (hasUrl) {
            // 情况一：仅有网址链接
            urlDisplay = domain;
            cardTypeClass = 'site-card-external';
        } else {
            // 无链接的卡片
            urlDisplay = '';
            cardTypeClass = 'site-card-no-link';
        }
        
        // 根据视图模式渲染不同内容
        if (this.isCompactMode) {
            // 紧凑模式：只显示图标和名称
            return `
                <div class="site-card ${cardTypeClass}" ${cardDataAttrs}>
                    <div class="site-header">
                        <div class="site-icon">${renderIcon(site.icon, site.name)}</div>
                        <div class="site-info">
                            <h3 class="site-title">${site.name}</h3>
                        </div>
                        ${hasMarkdown && hasUrl ? '<div class="doc-indicator" title="查看文档"><i class="fas fa-file-text"></i></div>' : ''}
                    </div>
                </div>
            `;
        } else {
            // 标准模式：显示完整信息
            return `
                <div class="site-card ${cardTypeClass}" ${cardDataAttrs}>
                    <div class="site-header">
                        <div class="site-icon">${renderIcon(site.icon, site.name)}</div>
                        <div class="site-info">
                            <h3 class="site-title">${site.name}</h3>
                            <div class="site-url">${urlDisplay}</div>
                        </div>
                        ${hasMarkdown && hasUrl ? '<div class="doc-indicator" title="查看文档"><i class="fas fa-file-text"></i><span>文档</span></div>' : ''}
                        ${hasMarkdown && !hasUrl ? '<div class="site-type-badge">📚 文档</div>' : ''}
                    </div>
                    <p class="site-description">${site.description || '暂无描述'}</p>
                    ${tags.length > 0 ? `
                        <div class="site-tags">
                            ${tags.map(tag => `<span class="site-tag">${tag}</span>`).join('')}
                        </div>
                    ` : ''}
                </div>
            `;
        }
    }
    
    /**
     * 绑定网站卡片事件
     */
    bindSiteCardEvents() {
        // 在重新绑定事件前，清除之前的悬停状态
        this.clearHoveredCard();

        const siteCards = this.sitesContainer.querySelectorAll('.site-card');

        siteCards.forEach(card => {
            card.addEventListener('click', (event) => {
                this.handleSiteCardClick(card, event);
            });

            // 添加鼠标悬停事件
            card.addEventListener('mouseenter', () => {
                this.setHoveredCard(card);
            });

            card.addEventListener('mouseleave', () => {
                this.clearHoveredCard();
            });

            // 添加键盘支持
            card.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    this.handleSiteCardClick(card, e);
                }
            });

            // 添加焦点支持
            card.setAttribute('tabindex', '0');
        });

        // 确保全局键盘事件监听器已绑定
        this.ensureGlobalKeyboardListener();

        console.log('网站卡片事件绑定完成，已清除悬停状态');
    }
    
    /**
     * 处理网站卡片点击
     * @param {Element} card 卡片元素
     */
    handleSiteCardClick(card, event) {
        const url = card.dataset.url;
        const markdownFile = card.dataset.markdownFile;
        const siteId = card.dataset.siteId;
        const siteName = card.dataset.siteName;

        // 检查是否点击了文档指示器
        const docIndicator = event?.target?.closest('.doc-indicator');
        
        if (docIndicator && markdownFile) {
            // 点击文档指示器，打开 Markdown 文档
            event.stopPropagation();
            this.markdownManager.loadAndShow(markdownFile, siteName || '文档');
            this.recordSiteVisit(siteId, 'markdown');
            this.visitManager.recordVisit(siteId, siteName || '文档', 'markdown');
            this.addClickAnimation(card);
            return;
        }

        // 主卡片点击逻辑
        const hasUrl = url && url.trim() !== '';
        const hasMarkdown = markdownFile && markdownFile.trim() !== '';

        if (hasUrl && hasMarkdown) {
            // 情况三：既有网址又有markdown，点击卡片打开网址
            window.open(formatUrl(url), '_blank');
            this.recordSiteVisit(siteId, 'external');
            this.visitManager.recordVisit(siteId, siteName || '网站', 'external');
            this.addClickAnimation(card);
        } else if (hasMarkdown && !hasUrl) {
            // 情况二：仅有markdown，点击卡片预览markdown
            this.markdownManager.loadAndShow(markdownFile, siteName || '文档');
            this.recordSiteVisit(siteId, 'markdown');
            this.visitManager.recordVisit(siteId, siteName || '文档', 'markdown');
            this.addClickAnimation(card);
        } else if (hasUrl && !hasMarkdown) {
            // 情况一：仅有网址，点击卡片打开网址
            window.open(formatUrl(url), '_blank');
            this.recordSiteVisit(siteId, 'external');
            this.visitManager.recordVisit(siteId, siteName || '网站', 'external');
            this.addClickAnimation(card);
        }
        // 如果既没有网址也没有markdown，则不执行任何操作
    }
    
    /**
     * 添加点击动画效果
     * @param {Element} card 卡片元素
     */
    addClickAnimation(card) {
        card.style.transform = 'scale(0.98)';
        setTimeout(() => {
            card.style.transform = '';
        }, 150);
    }
    
    /**
     * 记录网站访问统计
     * @param {string} siteId 网站ID
     * @param {string} type 访问类型 ('external' | 'markdown')
     */
    recordSiteVisit(siteId, type = 'external') {
        // 这里可以添加访问统计逻辑
        console.log(`网站访问: ${siteId}, 类型: ${type}`);
        
        // 可以发送到分析服务
        // analytics.track('site_visit', { siteId, type, timestamp: Date.now() });
    }
    
    /**
     * 根据分类ID获取网站列表
     * @param {string} categoryId 分类ID
     * @returns {Array} 网站列表
     */
    getSitesByCategory(categoryId) {
        // 特殊处理常用分类 - 始终返回基于访问历史的结果，即使为空
        if (categoryId === 'frequent') {
            const frequentSites = this.getFrequentSites();
            console.log(`常用分类返回 ${frequentSites.length} 个网站`);
            return frequentSites;
        }

        const category = this.getCategoryById(categoryId);
        if (!category) return [];

        // 如果分类有直接的网站，返回这些网站
        if (category.sites && category.sites.length > 0) {
            return category.sites;
        }

        // 如果分类有子分类，获取所有子分类的网站
        return this.getAllSitesFromCategory(categoryId);
    }
    
    /**
     * 获取指定分类及其所有子分类的网站
     * @param {string} categoryId 分类ID
     * @returns {Array} 所有网站列表
     */
    getAllSitesFromCategory(categoryId) {
        const allSites = [];

        // 递归收集分类和子分类的网站
        const collectSites = (catId) => {
            const category = this.flatCategories.find(cat => cat.id === catId);
            if (!category) return;

            // 添加当前分类的网站
            if (category.sites && category.sites.length > 0) {
                allSites.push(...category.sites);
            }

            // 递归添加子分类的网站
            const childCategories = this.flatCategories.filter(cat => cat.parentId === catId);
            childCategories.forEach(child => {
                collectSites(child.id);
            });
        };

        collectSites(categoryId);
        return allSites;
    }

    /**
     * 获取智能默认分类
     * 如果用户有访问历史，默认显示常用分类；否则显示全部分类
     * @returns {string} 默认分类ID
     */
    getSmartDefaultCategory() {
        // 检查是否有访问历史
        if (this.visitManager) {
            const visitStats = this.visitManager.getStats();
            if (visitStats.uniqueSites > 0) {
                console.log('检测到访问历史，默认显示常用分类');
                return 'frequent';
            }
        }

        // 首次访问或没有访问历史，显示全部分类
        console.log('首次访问或无访问历史，默认显示全部分类');
        return 'all-categories';
    }

    /**
     * 获取常用网站列表（基于访问历史）
     * @returns {Array} 常用网站列表
     */
    getFrequentSites() {
        if (!this.visitManager || !this.allSites) {
            return [];
        }

        // 获取最近访问的网站ID列表
        const recentSiteIds = this.visitManager.getRecentSiteIds(10);

        // 根据ID从所有网站中找到对应的网站对象
        const frequentSites = [];
        recentSiteIds.forEach(siteId => {
            const site = this.allSites.find(s => s.id === siteId);
            if (site) {
                // 添加访问信息到网站对象（用于排序和显示）
                const visitInfo = this.visitManager.getSiteVisitInfo(siteId);
                const siteWithVisitInfo = {
                    ...site,
                    visitInfo: visitInfo
                };
                frequentSites.push(siteWithVisitInfo);
            }
        });

        console.log(`获取常用网站: ${frequentSites.length} 个`);
        return frequentSites;
    }
    
    /**
     * 根据ID获取分类信息
     * @param {string} categoryId 分类ID
     * @returns {Object|null} 分类信息
     */
    getCategoryById(categoryId) {
        // 特殊处理常用分类
        if (categoryId === 'frequent') {
            return {
                id: 'frequent',
                name: '常用',
                icon: '⭐',
                sites: this.getFrequentSites(),
                isVirtual: true // 标记为虚拟分类
            };
        }

        const category = this.flatCategories.find(cat => cat.id === categoryId);
        if (!category) {
            console.warn(`分类 ${categoryId} 不存在`);
        }
        return category || null;
    }

    /**
     * 验证并修正当前分类
     * @param {string} categoryId 要验证的分类ID
     * @returns {string} 有效的分类ID
     */
    validateAndFixCategory(categoryId) {
        // 特殊处理所有分类模式和常用分类 - 这些都是有效的虚拟分类
        if (categoryId === 'all-categories' || categoryId === 'frequent') {
            console.log(`验证虚拟分类: ${categoryId} - 有效`);
            return categoryId;
        }

        // 检查分类是否存在
        const category = this.getCategoryById(categoryId);
        if (category) {
            return categoryId;
        }

        // 如果分类不存在，使用智能默认分类
        const smartDefault = this.getSmartDefaultCategory();
        console.log(`分类 ${categoryId} 不存在，使用智能默认分类: ${smartDefault}`);
        return smartDefault;
    }
    
    /**
     * 更新页面标题和计数
     * @param {Object} category 分类信息
     * @param {Array} sites 网站列表
     */
    updateHeader(category, sites) {
        console.log('更新页面标题:', category, sites);

        // 在单分类模式下显示内容页标题
        const contentHeader = document.querySelector('.content-header');
        if (contentHeader) {
            contentHeader.style.display = 'flex';
        }

        if (this.currentCategoryTitle) {
            const titleText = category ? category.name : '未知分类';
            this.currentCategoryTitle.textContent = titleText;
            console.log('设置标题为:', titleText);
        } else {
            console.error('currentCategoryTitle 元素未找到');
        }

        if (this.sitesCount) {
            this.sitesCount.textContent = sites.length.toString();
            console.log('设置网站数量为:', sites.length);
        } else {
            console.error('sitesCount 元素未找到');
        }
    }

    /**
     * 更新所有分类模式的标题
     */
    updateHeaderForAllCategories() {
        // 在全部分类模式下隐藏内容页标题
        const contentHeader = document.querySelector('.content-header');
        if (contentHeader) {
            contentHeader.style.display = 'none';
        }
    }

    /**
     * 初始化滚动监听器
     */
    initScrollListener() {
        // 移除之前的监听器
        if (this.scrollListener) {
            window.removeEventListener('scroll', this.scrollListener);
        }

        // 创建新的监听器，使用防抖和节流结合
        this.scrollListener = this.debounce(this.throttle(() => {
            this.handleScroll();
        }, 50), 10);

        window.addEventListener('scroll', this.scrollListener, { passive: true });

        // 初始化时执行一次，确保正确的初始状态
        setTimeout(() => {
            this.handleScroll();
        }, 100);

        console.log('滚动监听器已初始化');
    }

    /**
     * 处理滚动事件（平铺分类模式）
     */
    handleScroll() {
        if (!this.isShowingAllCategories) return;

        // 获取所有分类区域（包括占位符和实际分类）
        const allSections = document.querySelectorAll('.category-section');
        if (allSections.length === 0) return;

        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const windowHeight = window.innerHeight;
        const documentHeight = document.documentElement.scrollHeight;
        const navbarHeight = 64; // 导航栏高度
        const triggerPoint = scrollTop + navbarHeight + 100; // 触发点位置

        let currentCategoryId = null;
        let bestMatch = null;

        // 特殊处理：如果滚动到页面顶部
        if (scrollTop < 50) {
            // 选择第一个有内容的分类，或者第一个顶级分类
            const firstContentSection = Array.from(allSections).find(section =>
                section.querySelectorAll('.site-card').length > 0
            );
            if (firstContentSection) {
                currentCategoryId = firstContentSection.dataset.categoryId;
            } else {
                // 如果没有内容分类，选择第一个顶级分类
                currentCategoryId = this.getTopLevelCategoryForSection(allSections[0]);
            }
        }
        // 特殊处理：如果滚动到页面底部
        else if (scrollTop + windowHeight >= documentHeight - 50) {
            // 选择最后一个有内容的分类，或者最后一个顶级分类
            const lastContentSection = Array.from(allSections).reverse().find(section =>
                section.querySelectorAll('.site-card').length > 0
            );
            if (lastContentSection) {
                currentCategoryId = lastContentSection.dataset.categoryId;
            } else {
                currentCategoryId = this.getTopLevelCategoryForSection(allSections[allSections.length - 1]);
            }
        }
        // 正常情况：根据触发点查找当前分类
        else {
            // 遍历所有分类区域，找到最佳匹配
            for (let i = 0; i < allSections.length; i++) {
                const section = allSections[i];
                const rect = section.getBoundingClientRect();
                const sectionTop = rect.top + scrollTop;
                const sectionBottom = sectionTop + rect.height;
                const sitesCount = section.querySelectorAll('.site-card').length;

                // 如果是占位符（没有网站），跳过
                if (sitesCount === 0) continue;

                // 如果分类区域在触发点之上，记录为候选
                if (sectionTop <= triggerPoint) {
                    bestMatch = {
                        categoryId: section.dataset.categoryId,
                        distance: triggerPoint - sectionTop,
                        section: section
                    };
                }

                // 如果触发点在当前分类区域内，直接选择
                if (sectionTop <= triggerPoint && sectionBottom > triggerPoint) {
                    currentCategoryId = section.dataset.categoryId;
                    break;
                }

                // 如果分类区域在触发点之下，停止查找
                if (sectionTop > triggerPoint) {
                    break;
                }
            }

            // 如果没有直接匹配，使用最佳候选
            if (!currentCategoryId && bestMatch) {
                currentCategoryId = bestMatch.categoryId;
            }
        }

        // 更新侧边栏活动分类
        if (currentCategoryId && this.sidebarManager) {
            // 避免频繁更新相同的分类
            if (this.lastActiveCategoryId !== currentCategoryId) {
                // 直接使用当前分类ID，让侧边栏处理子分类的高亮和展开
                this.sidebarManager.setActiveCategory(currentCategoryId, true); // 标记为来自滚动事件
                this.lastActiveCategoryId = currentCategoryId;

                const category = this.getCategoryById(currentCategoryId);
                const categoryName = category ? category.name : currentCategoryId;
                console.log('滚动定位到分类:', categoryName, '(ID:', currentCategoryId, ')');
            }
        }
    }

    /**
     * 节流函数
     * @param {Function} func 要节流的函数
     * @param {number} wait 等待时间
     * @returns {Function} 节流后的函数
     */
    throttle(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * 防抖函数
     * @param {Function} func 要防抖的函数
     * @param {number} wait 等待时间
     * @returns {Function} 防抖后的函数
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    /**
     * 检查分类是否有子分类
     * @param {string} categoryId 分类ID
     * @returns {boolean} 是否有子分类
     */
    hasChildCategories(categoryId) {
        return this.flatCategories.some(cat => cat.parentId === categoryId);
    }

    /**
     * 检查一个分类是否是另一个分类的后代
     * @param {string} categoryId 要检查的分类ID
     * @param {string} ancestorId 祖先分类ID
     * @returns {boolean} 是否是后代分类
     */
    isDescendantOf(categoryId, ancestorId) {
        const category = this.flatCategories.find(cat => cat.id === categoryId);
        if (!category) return false;

        // 递归检查父分类链
        let currentParentId = category.parentId;
        while (currentParentId) {
            if (currentParentId === ancestorId) {
                return true;
            }
            const parentCategory = this.flatCategories.find(cat => cat.id === currentParentId);
            currentParentId = parentCategory ? parentCategory.parentId : null;
        }

        return false;
    }

    /**
     * 获取子分类对应的顶级分类ID
     * @param {string} categoryId 分类ID
     * @returns {string|null} 顶级分类ID，如果本身就是顶级分类则返回null
     */
    getTopLevelCategoryForChild(categoryId) {
        const category = this.flatCategories.find(cat => cat.id === categoryId);
        if (!category) return null;

        // 如果没有父分类，说明本身就是顶级分类
        if (!category.parentId) return null;

        // 递归查找顶级分类
        let currentCategory = category;
        while (currentCategory.parentId) {
            const parentCategory = this.flatCategories.find(cat => cat.id === currentCategory.parentId);
            if (!parentCategory) break;
            currentCategory = parentCategory;
        }

        return currentCategory.id;
    }

    /**
     * 为分类区域获取对应的顶级分类ID（用于占位符处理）
     * @param {Element} section 分类区域元素
     * @returns {string} 顶级分类ID
     */
    getTopLevelCategoryForSection(section) {
        if (!section) return null;

        const categoryId = section.dataset.categoryId;
        if (!categoryId) return null;

        // 如果是占位符或者子分类，返回对应的顶级分类
        const topLevelId = this.getTopLevelCategoryForChild(categoryId);
        return topLevelId || categoryId;
    }
    
    /**
     * 获取子分类数量
     * @param {string} categoryId 分类ID
     * @returns {number} 子分类数量
     */
    getChildCategoriesCount(categoryId) {
        return this.flatCategories.filter(cat => cat.parentId === categoryId).length;
    }
    
    /**
     * 切换分类
     * @param {string} categoryId 分类ID
     */
    switchCategory(categoryId) {
        // 清除悬停状态
        this.clearHoveredCard();

        // 检查是否切换到所有分类模式
        if (categoryId === 'all-categories') {
            this.currentCategory = categoryId;
            this.isShowingAllCategories = true;
            this.renderSites(categoryId);
            console.log('切换到所有分类模式');
            return;
        }

        // 检查是否切换到常用分类
        if (categoryId === 'frequent') {
            this.currentCategory = categoryId;
            this.isShowingAllCategories = false;
            this.renderSites(categoryId);
            console.log('切换到常用分类');
            return;
        }

        // 验证并修正分类ID
        const validCategoryId = this.validateAndFixCategory(categoryId);

        if (validCategoryId === this.currentCategory) return;

        this.currentCategory = validCategoryId;
        this.isShowingAllCategories = false;

        // 移除滚动监听器
        if (this.scrollListener) {
            window.removeEventListener('scroll', this.scrollListener);
            this.scrollListener = null;
        }

        this.renderSites(validCategoryId);

        console.log(`切换到分类: ${validCategoryId}`);
    }
    
    /**
     * 显示所有网站（用于搜索重置）
     */
    showAllSites() {
        this.renderSites(this.currentCategory);
    }
    
    /**
     * 显示加载状态
     * @param {boolean} show 是否显示
     */
    showLoading(show) {
        if (this.loadingSpinner) {
            this.loadingSpinner.style.display = show ? 'flex' : 'none';
        }
        this.isLoading = show;
    }
    
    /**
     * 显示空状态
     */
    showEmptyState() {
        if (this.emptyState) {
            // 根据当前分类显示不同的空状态信息
            const emptyStateIcon = this.emptyState.querySelector('i');
            const emptyStateTitle = this.emptyState.querySelector('h3');
            const emptyStateDesc = this.emptyState.querySelector('p');

            if (this.currentCategory === 'frequent') {
                // 常用分类的空状态
                if (emptyStateIcon) emptyStateIcon.className = 'fas fa-star fa-3x text-muted';
                if (emptyStateTitle) emptyStateTitle.textContent = '暂无常用网站';
                if (emptyStateDesc) emptyStateDesc.textContent = '点击任意网站卡片后，它们将出现在这里';
            } else {
                // 其他分类的空状态
                if (emptyStateIcon) emptyStateIcon.className = 'fas fa-search fa-3x text-muted';
                if (emptyStateTitle) emptyStateTitle.textContent = '没有找到相关网站';
                if (emptyStateDesc) emptyStateDesc.textContent = '尝试调整搜索关键词或选择其他分类';
            }

            this.emptyState.style.display = 'block';
        }
        if (this.sitesContainer) {
            this.sitesContainer.style.display = 'none';
        }
    }
    
    /**
     * 隐藏空状态
     */
    hideEmptyState() {
        if (this.emptyState) {
            this.emptyState.style.display = 'none';
        }
        if (this.sitesContainer) {
            // 移除内联样式，让CSS类控制布局
            this.sitesContainer.style.display = '';
        }
    }
    
    /**
     * 显示错误消息
     * @param {string} message 错误消息
     */
    showErrorMessage(message) {
        if (typeof showToast === 'function') {
            showToast(message, 'error', 5000);
        } else {
            alert(message);
        }
    }
    
    /**
     * 绑定全局事件
     */
    bindEvents() {
        // 视图切换按钮点击
        if (this.viewToggleBtn) {
            this.viewToggleBtn.addEventListener('click', () => {
                this.toggleViewMode();
            });
        }

        // 帮助按钮点击
        const helpBtn = document.querySelector('.help-btn');
        if (helpBtn) {
            helpBtn.addEventListener('click', () => {
                this.showHelpModal();
            });
        }

        // 绑定卡片快捷键事件
        this.bindCardKeyboardEvents();
        
        // 错误处理
        window.addEventListener('error', (e) => {
            console.error('全局错误:', e.error);
        });
        
        // 未处理的Promise rejection
        window.addEventListener('unhandledrejection', (e) => {
            console.error('未处理的Promise错误:', e.reason);
        });
        
        console.log('全局事件绑定完成');
    }


    
    /**
     * 显示帮助模态框
     */
    showHelpModal() {
        // 检查 MarkdownManager 是否可用
        if (this.markdownManager) {
            // 使用 Markdown 阅读器显示使用指南
            this.markdownManager.loadAndShow('data/docs/usage_guide.md', 'FaciShare 导航使用指南');
        } else {
            // 降级到原有的简短说明
            this.showFallbackHelp();
        }
    }

    /**
     * 显示降级帮助信息（当 Markdown 管理器不可用时）
     */
    showFallbackHelp() {
        // 使用平台检测获取快捷键
        const modifier = typeof Platform !== 'undefined' ? Platform.getModifierKey() : '⌘/Ctrl';

        let helpMessage = `快捷键：${modifier}+K 搜索，ESC 清空搜索/关闭侧边栏，${modifier}+ESC 重置主题`;

        // 显示当前主题信息
        if (this.themeManager) {
            const themeSource = this.themeManager.getThemeSource();
            helpMessage += `\n当前主题：${themeSource.theme === 'dark' ? '深色' : '浅色'} (${themeSource.description})`;

            if (themeSource.source === 'time') {
                const config = this.themeManager.getTimeThemeConfig();
                helpMessage += `\n时间设置：${config.lightStart}:00-${config.lightEnd}:00 使用浅色主题`;
            }
        }

        // 显示平台信息
        if (typeof Platform !== 'undefined') {
            helpMessage += `\n平台：${Platform.getPlatformName()}`;
        }

        if (typeof showToast === 'function') {
            showToast(helpMessage, 'info', 5000);
        } else {
            alert(helpMessage);
        }
    }
    
    /**
     * 获取应用统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        return {
            totalCategories: this.flatCategories.length,
            totalSites: this.allSites.length,
            currentCategory: this.currentCategory,
            theme: this.themeManager ? this.themeManager.getCurrentTheme() : 'unknown'
        };
    }
    
    /**
     * 刷新数据
     */
    async refresh() {
        try {
            await this.loadData();
            this.render();
            showToast('数据刷新成功', 'success');
        } catch (error) {
            console.error('刷新失败:', error);
            this.showErrorMessage('刷新失败，请稍后重试');
        }
    }
    
    /**
     * 移动端调试信息
     */
    getMobileDebugInfo() {
        return {
            // 设备信息
            device: {
                userAgent: navigator.userAgent,
                screenWidth: window.screen.width,
                screenHeight: window.screen.height,
                windowWidth: window.innerWidth,
                windowHeight: window.innerHeight,
                isMobile: window.innerWidth <= 767,
                isTouchDevice: 'ontouchstart' in window
            },

            // 应用状态
            app: {
                currentCategory: this.currentCategory,
                isLoading: this.isLoading,
                dataLoaded: !!this.data,
                categoriesCount: this.flatCategories ? this.flatCategories.length : 0,
                sitesCount: this.allSites ? this.allSites.length : 0
            },

            // DOM元素状态
            elements: {
                sitesContainer: !!this.sitesContainer,
                currentCategoryTitle: !!this.currentCategoryTitle,
                sitesCount: !!this.sitesCount,
                loadingSpinner: !!this.loadingSpinner,
                emptyState: !!this.emptyState
            },

            // 管理器状态
            managers: {
                themeManager: !!this.themeManager,
                searchManager: !!this.searchManager,
                sidebarManager: !!this.sidebarManager,
                markdownManager: !!this.markdownManager
            }
        };
    }

    /**
     * 销毁应用（清理资源）
     */
    destroy() {
        // 清理滚动监听器
        if (this.scrollListener) {
            window.removeEventListener('scroll', this.scrollListener);
            this.scrollListener = null;
        }

        // 清理卡片键盘事件监听器
        if (this.cardKeyboardListener) {
            document.removeEventListener('keydown', this.cardKeyboardListener);
            this.cardKeyboardListener = null;
        }

        // 清理悬停状态
        this.clearHoveredCard();

        // 清理事件监听器和资源
        if (this.themeManager) {
            this.themeManager = null;
        }
        if (this.searchManager) {
            this.searchManager = null;
        }
        if (this.sidebarManager) {
            this.sidebarManager = null;
        }
        if (this.timeNotificationManager) {
            this.timeNotificationManager.destroy();
            this.timeNotificationManager = null;
        }

        console.log('应用资源已清理');
    }

    /**
     * 切换视图模式
     */
    toggleViewMode() {
        this.isCompactMode = !this.isCompactMode;
        this.updateViewModeUI();
        this.applyViewMode();

        // 保存视图模式到本地存储
        localStorage.setItem('navSphere_viewMode', this.isCompactMode ? 'compact' : 'standard');

        console.log('视图模式已切换:', this.isCompactMode ? '紧凑模式' : '标准模式');
    }

    /**
     * 更新视图模式UI
     */
    updateViewModeUI() {
        if (!this.viewToggleBtn || !this.viewToggleIcon || !this.viewModeText) return;

        if (this.isCompactMode) {
            // 紧凑模式
            this.viewToggleBtn.classList.add('compact-mode');
            this.viewToggleIcon.className = 'fas fa-th-list';
            this.viewModeText.textContent = '紧凑';
            this.viewToggleBtn.title = '切换到标准视图';
        } else {
            // 标准模式
            this.viewToggleBtn.classList.remove('compact-mode');
            this.viewToggleIcon.className = 'fas fa-th-large';
            this.viewModeText.textContent = '标准';
            this.viewToggleBtn.title = '切换到紧凑视图';
        }
    }

    /**
     * 应用视图模式
     */
    applyViewMode() {
        if (!this.sitesContainer) return;

        // 添加过渡效果
        this.sitesContainer.style.transition = 'all 0.3s ease-out';

        if (this.isCompactMode) {
            this.sitesContainer.classList.add('compact-view');
        } else {
            this.sitesContainer.classList.remove('compact-view');
        }

        // 延迟重新渲染以确保CSS过渡效果完成
        setTimeout(() => {
            this.refreshCurrentView();
        }, 50);
    }

    /**
     * 刷新当前视图
     */
    refreshCurrentView() {
        // 在刷新视图前清除悬停状态
        this.clearHoveredCard();

        if (this.isShowingAllCategories) {
            // 如果当前显示所有分类，重新渲染所有分类
            this.renderAllCategoriesSites();
        } else {
            // 如果显示特定分类，重新渲染该分类
            // 直接调用renderSites而不是switchCategory，避免重复的状态检查和设置
            this.renderSites(this.currentCategory);
        }

        // 确保键盘事件监听器在视图刷新后仍然有效
        this.ensureGlobalKeyboardListener();
    }

    /**
     * 调试方法：检查当前状态
     */
    debugCurrentState() {
        console.log('=== 当前状态调试信息 ===');
        console.log('版本: 视图模式Bug修复版本 v1.0');
        console.log('isCompactMode:', this.isCompactMode);
        console.log('isShowingAllCategories:', this.isShowingAllCategories);
        console.log('currentCategory:', this.currentCategory);
        console.log('sitesContainer classes:', this.sitesContainer?.className);
        console.log('========================');
    }

    /**
     * 初始化视图模式（从本地存储恢复）
     */
    initViewMode() {
        const savedMode = localStorage.getItem('navSphere_viewMode');
        if (savedMode === 'compact') {
            this.isCompactMode = true;
        }

        this.updateViewModeUI();
        this.applyViewMode();
    }

    /**
     * 初始化数据管理UI
     */
    initDataManagementUI() {
        const dataManageBtn = document.getElementById('dataManageBtn');
        const dataManageMenu = document.getElementById('dataManageMenu');
        const importFile = document.getElementById('importFile');

        if (dataManageBtn && dataManageMenu) {
            // 点击按钮显示/隐藏菜单
            dataManageBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                dataManageBtn.parentElement.classList.toggle('show');
            });

            // 点击外部关闭菜单
            document.addEventListener('click', (e) => {
                if (!dataManageBtn.parentElement.contains(e.target)) {
                    dataManageBtn.parentElement.classList.remove('show');
                }
            });

            // 阻止菜单内部点击事件冒泡
            dataManageMenu.addEventListener('click', (e) => {
                e.stopPropagation();
            });
        }

        // 文件导入处理
        if (importFile) {
            importFile.addEventListener('change', (e) => {
                const file = e.target.files[0];
                if (file) {
                    this.visitManager.importData(file);
                    e.target.value = ''; // 清空文件选择
                    // 关闭菜单
                    dataManageBtn.parentElement.classList.remove('show');
                }
            });
        }

        console.log('数据管理UI初始化完成');
    }

    /**
     * 启动自动备份机制
     */
    startAutoBackup() {
        // 页面加载时立即备份一次
        if (this.visitManager) {
            this.visitManager.backupToIndexedDB();
        }

        // 每小时检查一次是否需要备份
        setInterval(() => {
            if (!this.visitManager) return;

            const lastBackup = localStorage.getItem('navsphere-last-auto-backup');
            const now = Date.now();

            // 24小时自动备份一次，或者访问历史有变化时
            if (!lastBackup || now - parseInt(lastBackup) > 24 * 60 * 60 * 1000) {
                this.visitManager.backupToIndexedDB();
                localStorage.setItem('navsphere-last-auto-backup', now.toString());
                console.log('执行定时自动备份');
            }
        }, 60 * 60 * 1000); // 每小时检查一次

        console.log('自动备份机制已启动');
    }

    /**
     * 初始化页面关闭前备份
     */
    initBeforeUnloadBackup() {
        window.addEventListener('beforeunload', () => {
            // 页面关闭前进行备份
            if (this.visitManager) {
                // 使用同步方式备份，确保在页面关闭前完成
                try {
                    this.visitManager.backupToIndexedDB();
                    console.log('页面关闭前备份完成');
                } catch (error) {
                    console.error('页面关闭前备份失败:', error);
                }
            }
        });

        // 页面可见性变化时也进行备份
        document.addEventListener('visibilitychange', () => {
            if (document.hidden && this.visitManager) {
                // 页面隐藏时备份
                this.visitManager.backupToIndexedDB();
                console.log('页面隐藏时备份完成');
            }
        });

        console.log('页面关闭前备份机制已初始化');
    }

    /**
     * 设置当前悬停的卡片
     * @param {Element} card 卡片元素
     */
    setHoveredCard(card) {
        this.hoveredCard = card;
        // 添加悬停状态样式
        card.classList.add('card-hovered');

        // 显示快捷键提示（可选）
        this.showCardShortcutHint(card);
    }

    /**
     * 清除悬停的卡片
     */
    clearHoveredCard() {
        if (this.hoveredCard) {
            this.hoveredCard.classList.remove('card-hovered');
            this.hideCardShortcutHint();
            this.hoveredCard = null;
        }
    }

    /**
     * 绑定卡片键盘事件
     */
    bindCardKeyboardEvents() {
        // 移除之前的监听器
        if (this.cardKeyboardListener) {
            document.removeEventListener('keydown', this.cardKeyboardListener, true);
            this.cardKeyboardListener = null;
        }

        // 创建新的键盘事件监听器 - 使用箭头函数保持this上下文
        this.cardKeyboardListener = (e) => {
            this.handleCardKeyboardShortcuts(e);
        };

        // 使用 capture 阶段来确保主应用的事件监听器优先执行
        document.addEventListener('keydown', this.cardKeyboardListener, true);
        console.log('卡片键盘快捷键事件已绑定（capture阶段）');
    }

    /**
     * 确保全局键盘监听器已绑定（防止重复渲染时丢失）
     */
    ensureGlobalKeyboardListener() {
        if (!this.cardKeyboardListener) {
            console.log('检测到键盘监听器丢失，重新绑定');
            this.bindCardKeyboardEvents();
        }
    }

    /**
     * 处理卡片键盘快捷键
     * @param {KeyboardEvent} e 键盘事件
     */
    handleCardKeyboardShortcuts(e) {
        // 只在有悬停卡片时处理
        if (!this.hoveredCard) {
            return;
        }

        // 检查是否应该处理快捷键（避免与输入框等冲突）
        if (!this.shouldHandleCardShortcuts(e)) {
            return;
        }

        const card = this.hoveredCard;
        const url = card.dataset.url;
        const markdownFile = card.dataset.markdownFile;
        const siteId = card.dataset.siteId;
        const siteName = card.dataset.siteName;

        switch (e.key) {
            case ' ': // 空格键 - 预览 Markdown 文档
                e.preventDefault();
                e.stopPropagation(); // 阻止事件传播到搜索管理器
                this.handleSpaceKeyOnCard(card, markdownFile, siteId, siteName);
                break;

            case 'Enter': // 回车键 - 打开网址链接
                e.preventDefault();
                e.stopPropagation(); // 阻止事件传播
                this.handleEnterKeyOnCard(card, url, siteId, siteName);
                break;
        }
    }

    /**
     * 检查是否应该处理卡片快捷键
     * @param {KeyboardEvent} e 键盘事件
     * @returns {boolean} 是否应该处理
     */
    shouldHandleCardShortcuts(e) {
        // 如果有元素聚焦且是输入类元素，不处理快捷键
        const activeElement = document.activeElement;
        if (activeElement && (
            activeElement.tagName === 'INPUT' ||
            activeElement.tagName === 'TEXTAREA' ||
            activeElement.contentEditable === 'true'
        )) {
            return false;
        }

        // 如果搜索结果或筛选器可见，不处理快捷键
        if (this.searchManager && (
            this.searchManager.isSearchVisible ||
            this.searchManager.isFiltersVisible
        )) {
            return false;
        }

        // 如果 Spotlight 搜索覆盖层打开，不处理快捷键
        const spotlightOverlay = document.getElementById('spotlightOverlay');
        if (spotlightOverlay && spotlightOverlay.style.display !== 'none') {
            return false;
        }

        // 如果 Markdown 模态框打开，空格键应该用于关闭模态框，不处理卡片快捷键
        if (this.markdownManager && this.markdownManager.isModalOpen()) {
            return false;
        }

        // 如果移动端侧边栏打开，不处理快捷键
        if (this.sidebarManager && this.sidebarManager.isMobileOpen()) {
            return false;
        }

        return true;
    }

    /**
     * 处理空格键在卡片上的操作
     * @param {Element} card 卡片元素
     * @param {string} markdownFile Markdown文件路径
     * @param {string} siteId 网站ID
     * @param {string} siteName 网站名称
     */
    handleSpaceKeyOnCard(card, markdownFile, siteId, siteName) {
        if (markdownFile && markdownFile.trim() !== '') {
            // 有 Markdown 文档，打开预览
            if (this.markdownManager) {
                this.markdownManager.loadAndShow(markdownFile, siteName || '文档');
                this.recordSiteVisit(siteId, 'markdown');
                this.visitManager.recordVisit(siteId, siteName || '文档', 'markdown');
                this.addClickAnimation(card);
                this.showOperationHint('已打开文档预览', 'success');
            } else {
                this.showOperationHint('Markdown 管理器不可用', 'error');
            }
        } else {
            // 没有 Markdown 文档，显示提示
            this.showOperationHint('该网站没有配置文档', 'info');
        }
    }

    /**
     * 处理回车键在卡片上的操作
     * @param {Element} card 卡片元素
     * @param {string} url 网址链接
     * @param {string} siteId 网站ID
     * @param {string} siteName 网站名称
     */
    handleEnterKeyOnCard(card, url, siteId, siteName) {
        if (url && url.trim() !== '') {
            // 有网址链接，打开链接
            window.open(formatUrl(url), '_blank');
            this.recordSiteVisit(siteId, 'external');
            this.visitManager.recordVisit(siteId, siteName || '网站', 'external');
            this.addClickAnimation(card);
            this.showOperationHint('已在新标签页打开网站', 'success');
        } else {
            // 没有网址链接，显示提示
            this.showOperationHint('该网站没有配置链接', 'info');
        }
    }

    /**
     * 显示卡片快捷键提示
     * @param {Element} card 卡片元素
     */
    showCardShortcutHint(card) {
        // 检查是否已经有提示
        if (card.querySelector('.card-shortcut-hint')) {
            return;
        }

        const url = card.dataset.url;
        const markdownFile = card.dataset.markdownFile;
        
        // 构建提示内容
        const hints = [];
        if (markdownFile && markdownFile.trim() !== '') {
            hints.push('空格键：查看文档');
        }
        if (url && url.trim() !== '') {
            hints.push('回车键：打开链接');
        }

        if (hints.length === 0) {
            return; // 没有可用操作，不显示提示
        }

        // 创建提示元素
        const hintElement = document.createElement('div');
        hintElement.className = 'card-shortcut-hint';
        hintElement.innerHTML = `
            <div class="shortcut-hint-content">
                <i class="fas fa-keyboard"></i>
                <span>${hints.join(' | ')}</span>
            </div>
        `;

        // 添加到卡片
        card.appendChild(hintElement);

        // 添加显示动画
        setTimeout(() => {
            hintElement.classList.add('show');
        }, 100);
    }

    /**
     * 隐藏卡片快捷键提示
     */
    hideCardShortcutHint() {
        if (this.hoveredCard) {
            const hintElement = this.hoveredCard.querySelector('.card-shortcut-hint');
            if (hintElement) {
                hintElement.classList.remove('show');
                setTimeout(() => {
                    if (hintElement.parentNode) {
                        hintElement.parentNode.removeChild(hintElement);
                    }
                }, 200);
            }
        }
    }

    /**
     * 显示操作提示信息
     * @param {string} message 提示消息
     * @param {string} type 提示类型 ('success', 'info', 'warning', 'error')
     */
    showOperationHint(message, type = 'info') {
        // 尝试使用全局Toast功能
        if (typeof showToast === 'function') {
            showToast(message, type, 2000);
            return;
        }

        // 创建临时提示元素
        const hintElement = document.createElement('div');
        hintElement.className = `operation-hint operation-hint-${type}`;
        hintElement.innerHTML = `
            <div class="hint-content">
                <i class="fas ${this.getHintIcon(type)}"></i>
                <span>${message}</span>
            </div>
        `;

        // 添加到页面
        document.body.appendChild(hintElement);

        // 显示动画
        setTimeout(() => {
            hintElement.classList.add('show');
        }, 10);

        // 自动移除
        setTimeout(() => {
            hintElement.classList.remove('show');
            setTimeout(() => {
                if (hintElement.parentNode) {
                    hintElement.parentNode.removeChild(hintElement);
                }
            }, 300);
        }, 2000);
    }

    /**
     * 获取提示图标
     * @param {string} type 提示类型
     * @returns {string} 图标类名
     */
    getHintIcon(type) {
        const iconMap = {
            'success': 'fa-check-circle',
            'info': 'fa-info-circle',
            'warning': 'fa-exclamation-triangle',
            'error': 'fa-times-circle'
        };
        return iconMap[type] || 'fa-info-circle';
    }
}

// 全局应用实例
let navApp;

// 应用启动
document.addEventListener('DOMContentLoaded', () => {
    navApp = new NavApp();
    // 将实例暴露给全局，供其他模块使用
    window.navApp = navApp;
});

// 全局访问接口
window.NavApp = {
    getInstance: () => navApp,
    refresh: () => navApp?.refresh(),
    getStats: () => navApp?.getStats(),
    switchTheme: () => navApp?.themeManager?.toggleTheme(),

    // 调试方法
    debug: () => navApp?.getMobileDebugInfo(),
    debugState: () => navApp?.debugCurrentState(),
    
    // 主题相关接口
    theme: {
        // 手动切换主题
        toggle: () => navApp?.themeManager?.toggleTheme(),
        
        // 设置特定主题
        set: (theme) => navApp?.themeManager?.setTheme(theme, true),
        
        // 重置为自动主题
        reset: () => navApp?.themeManager?.resetTheme(),
        
        // 获取当前主题信息
        getInfo: () => navApp?.themeManager?.getThemeSource(),
        
        // 配置时间主题
        configTime: (config) => {
            if (!navApp?.themeManager) return;
            navApp.themeManager.updateTimeThemeConfig(config);
            console.log('时间主题配置示例:');
            console.log('NavApp.theme.configTime({ enabled: true, lightStart: 7, lightEnd: 19 })');
        },
        
        // 获取时间主题配置
        getTimeConfig: () => navApp?.themeManager?.getTimeThemeConfig()
    },
    
    // 常用分类相关接口
    frequent: {
        // 获取常用网站列表
        getSites: () => navApp?.getFrequentSites() || [],

        // 获取访问统计
        getStats: () => navApp?.visitManager?.getStats() || {},

        // 清除访问历史
        clearHistory: () => {
            if (navApp?.visitManager) {
                navApp.visitManager.clearHistory();
                return '访问历史已清除';
            }
            return '访问管理器未初始化';
        },

        // 模拟访问（用于测试）
        simulateVisit: (siteId, siteName, type = 'external') => {
            if (navApp?.visitManager) {
                navApp.visitManager.recordVisit(siteId, siteName, type);
                return `已记录访问: ${siteName}`;
            }
            return '访问管理器未初始化';
        }
    },

    // 搜索和筛选相关接口
    search: {
        // 重置标签筛选器使用状态
        resetTagHints: () => {
            if (navApp?.searchManager) {
                navApp.searchManager.resetTagFiltersUsage();
                return '标签筛选器使用提示已重置';
            }
            return '搜索管理器未初始化';
        },

        // 获取搜索统计
        getStats: () => {
            if (navApp?.searchManager) {
                return {
                    hasUsedTagFilters: navApp.searchManager.hasUsedTagFilters,
                    currentQuery: navApp.searchManager.getCurrentQuery(),
                    filtersActive: {
                        category: !!navApp.searchManager.filters.category,
                        tagsCount: navApp.searchManager.filters.tags.size
                    }
                };
            }
            return null;
        }
    },

    // 多文件数据源管理接口
    dataSources: {
        // 获取数据源信息
        getInfo: () => navApp?.getDataSourceInfo() || null,

        // 重新加载数据源
        reload: () => navApp?.reloadDataSources(),

        // 检查是否使用多文件配置
        isMultiFile: () => {
            const info = navApp?.getDataSourceInfo();
            return info ? info.hasMultipleDataSources : false;
        },

        // 获取当前配置摘要
        getSummary: () => {
            const info = navApp?.getDataSourceInfo();
            if (!info) return '数据源信息不可用';

            const summary = [
                `数据源模式: ${info.hasMultipleDataSources ? '多文件' : '单文件'}`,
                `数据源数量: ${info.dataSources.length}`,
                `总分类数: ${info.totalCategories}`,
                `总网站数: ${info.totalSites}`,
                `加载时间: ${info.loadedAt || '未知'}`
            ];

            if (info.hasMultipleDataSources) {
                summary.push('数据源列表:');
                info.dataSources.forEach(ds => {
                    summary.push(`  - ${ds.name} (${ds.path}) [优先级: ${ds.priority}]`);
                });
            }

            return summary.join('\n');
        }
    }
};

// 导出应用类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = NavApp;
} 