/**
 * 访问记录管理器
 * 负责记录、存储和管理用户的网站访问历史
 * 支持数据持久化、备份恢复等功能
 */
class VisitManager {
    constructor() {
        this.storageKey = 'navsphere-visit-history';
        this.maxRecentSites = 20; // 最多保存20个最近访问的网站
        this.dbName = 'NavSphereDB';
        this.dbVersion = 1;
        this.visitHistory = [];
        this.backupTimer = null; // 备份定时器

        // 异步初始化
        this.initWithRecovery();
    }

    /**
     * 异步初始化，包含数据恢复机制
     */
    async initWithRecovery() {
        // 检查localStorage是否可用
        if (!this.checkDataHealth()) {
            console.warn('检测到存储问题，尝试从备份恢复...');
            if (typeof showToast === 'function') {
                showToast('检测到存储问题，尝试从备份恢复...', 'warning');
            }
            const recovered = await this.recoverFromIndexedDB();
            if (!recovered) {
                console.error('无法恢复数据，请手动导入备份文件');
                if (typeof showToast === 'function') {
                    showToast('无法恢复数据，请手动导入备份文件', 'error');
                }
            }
            return;
        }

        // 正常加载数据
        this.visitHistory = this.loadVisitHistory();

        // 检查数据是否为空但应该有数据（可能数据丢失）
        // 注意：不再自动恢复，避免影响用户清除历史的操作
        if (this.visitHistory.length === 0) {
            // 检查是否有备份数据可用（仅记录日志，不自动恢复）
            try {
                const db = await this.openIndexedDB();
                const transaction = db.transaction(['visits'], 'readonly');
                const visitStore = transaction.objectStore('visits');
                const request = visitStore.get('backup');
                const result = await this.promisifyRequest(request);

                if (result && result.data && Array.isArray(result.data) && result.data.length > 0) {
                    console.log(`检测到可用备份数据: ${result.data.length} 条记录，备份时间: ${new Date(result.timestamp).toLocaleString()}`);
                    console.log('如需恢复数据，请使用"数据"菜单中的"恢复备份"功能');
                }
                db.close();
            } catch (error) {
                console.log('检查备份数据时出错:', error);
            }
        }

        // 初始化完成后进行一次备份
        this.backupToIndexedDB();
    }

    /**
     * 检查数据健康状态
     * @returns {boolean} localStorage是否可用
     */
    checkDataHealth() {
        try {
            const testKey = 'navsphere-health-check';
            localStorage.setItem(testKey, 'test');
            localStorage.removeItem(testKey);
            return true;
        } catch (error) {
            console.error('localStorage不可用:', error);
            return false;
        }
    }

    /**
     * 从本地存储加载访问历史
     * @returns {Array} 访问历史数组
     */
    loadVisitHistory() {
        try {
            const saved = localStorage.getItem(this.storageKey);
            if (saved) {
                const history = JSON.parse(saved);
                // 验证数据格式
                if (Array.isArray(history)) {
                    console.log(`访问历史已加载: ${history.length} 条记录`);
                    return history;
                }
            }
        } catch (error) {
            console.error('加载访问历史失败:', error);
        }

        console.log('初始化空的访问历史');
        return [];
    }

    /**
     * 保存访问历史到本地存储
     */
    saveVisitHistory() {
        try {
            localStorage.setItem(this.storageKey, JSON.stringify(this.visitHistory));
            console.log(`访问历史已保存: ${this.visitHistory.length} 条记录`);
        } catch (error) {
            console.error('保存访问历史失败:', error);
        }
    }

    /**
     * 记录网站访问
     * @param {string} siteId 网站ID
     * @param {string} siteName 网站名称
     * @param {string} type 访问类型 ('external' | 'markdown')
     */
    recordVisit(siteId, siteName, type = 'external') {
        if (!siteId) {
            console.warn('记录访问失败: siteId 为空');
            return;
        }

        const now = Date.now();
        
        // 查找是否已存在该网站的记录
        const existingIndex = this.visitHistory.findIndex(item => item.siteId === siteId);
        
        if (existingIndex !== -1) {
            // 如果已存在，更新访问时间和次数，并移到最前面
            const existingItem = this.visitHistory[existingIndex];
            existingItem.lastVisit = now;
            existingItem.visitCount = (existingItem.visitCount || 1) + 1;
            existingItem.siteName = siteName; // 更新网站名称（可能会变化）
            existingItem.type = type; // 更新访问类型
            
            // 移到数组最前面
            this.visitHistory.splice(existingIndex, 1);
            this.visitHistory.unshift(existingItem);
        } else {
            // 如果不存在，创建新记录并添加到最前面
            const newVisit = {
                siteId,
                siteName,
                type,
                firstVisit: now,
                lastVisit: now,
                visitCount: 1
            };
            
            this.visitHistory.unshift(newVisit);
        }

        // 限制历史记录数量
        if (this.visitHistory.length > this.maxRecentSites) {
            this.visitHistory = this.visitHistory.slice(0, this.maxRecentSites);
        }

        // 保存到本地存储
        this.saveVisitHistory();

        // 触发增量备份（防抖处理，避免频繁备份）
        this.scheduleBackup();

        console.log(`记录访问: ${siteName} (${siteId}), 类型: ${type}`);
    }

    /**
     * 获取最近访问的网站列表
     * @param {number} limit 返回数量限制，默认为10
     * @returns {Array} 最近访问的网站ID列表
     */
    getRecentSites(limit = 10) {
        return this.visitHistory
            .slice(0, limit)
            .map(item => ({
                siteId: item.siteId,
                siteName: item.siteName,
                type: item.type,
                lastVisit: item.lastVisit,
                visitCount: item.visitCount
            }));
    }

    /**
     * 获取最近访问的网站ID列表
     * @param {number} limit 返回数量限制，默认为10
     * @returns {Array} 网站ID数组
     */
    getRecentSiteIds(limit = 10) {
        return this.visitHistory
            .slice(0, limit)
            .map(item => item.siteId);
    }

    /**
     * 获取访问统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        return {
            totalVisits: this.visitHistory.reduce((sum, item) => sum + (item.visitCount || 1), 0),
            uniqueSites: this.visitHistory.length,
            recentSites: Math.min(this.visitHistory.length, 10)
        };
    }



    /**
     * 检查网站是否在最近访问列表中
     * @param {string} siteId 网站ID
     * @returns {boolean} 是否在最近访问列表中
     */
    isRecentSite(siteId) {
        return this.visitHistory.some(item => item.siteId === siteId);
    }

    /**
     * 获取网站的访问信息
     * @param {string} siteId 网站ID
     * @returns {Object|null} 访问信息
     */
    getSiteVisitInfo(siteId) {
        const visitInfo = this.visitHistory.find(item => item.siteId === siteId);
        return visitInfo || null;
    }

    /**
     * 打开IndexedDB数据库
     * @returns {Promise<IDBDatabase>} 数据库实例
     */
    async openIndexedDB() {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open(this.dbName, this.dbVersion);

            request.onerror = () => {
                console.error('IndexedDB打开失败:', request.error);
                reject(request.error);
            };

            request.onsuccess = () => {
                resolve(request.result);
            };

            request.onupgradeneeded = (event) => {
                const db = event.target.result;

                // 创建访问历史存储
                if (!db.objectStoreNames.contains('visits')) {
                    const visitStore = db.createObjectStore('visits', { keyPath: 'id' });
                    console.log('创建visits对象存储');
                }

                // 创建设置存储
                if (!db.objectStoreNames.contains('settings')) {
                    const settingsStore = db.createObjectStore('settings', { keyPath: 'key' });
                    console.log('创建settings对象存储');
                }
            };
        });
    }

    /**
     * 备份数据到IndexedDB
     */
    async backupToIndexedDB() {
        if (!this.visitHistory || this.visitHistory.length === 0) {
            console.log('没有数据需要备份');
            return false;
        }

        try {
            const db = await this.openIndexedDB();
            const transaction = db.transaction(['visits', 'settings'], 'readwrite');

            // 备份访问历史
            const visitStore = transaction.objectStore('visits');
            await this.promisifyRequest(visitStore.put({
                id: 'backup',
                data: this.visitHistory,
                timestamp: Date.now()
            }));

            // 备份其他设置
            const settingsStore = transaction.objectStore('settings');
            const settingsToBackup = [
                { key: 'navsphere-theme', value: localStorage.getItem('navsphere-theme') },
                { key: 'navSphere_viewMode', value: localStorage.getItem('navSphere_viewMode') },
                { key: 'navsphere-current-category', value: localStorage.getItem('navsphere-current-category') },
                { key: 'navsphere-expanded-categories', value: localStorage.getItem('navsphere-expanded-categories') }
            ];

            for (const setting of settingsToBackup) {
                if (setting.value) {
                    await this.promisifyRequest(settingsStore.put({
                        key: setting.key,
                        value: setting.value,
                        timestamp: Date.now()
                    }));
                }
            }

            console.log('数据已备份到IndexedDB');
            db.close();
            return true;
        } catch (error) {
            console.error('IndexedDB备份失败:', error);
            return false;
        }
    }

    /**
     * 从IndexedDB恢复数据
     * @returns {Promise<boolean>} 是否成功恢复
     */
    async recoverFromIndexedDB() {
        try {
            const db = await this.openIndexedDB();
            const transaction = db.transaction(['visits', 'settings'], 'readonly');

            // 先检查是否有备份数据
            const visitStore = transaction.objectStore('visits');
            const visitRequest = visitStore.get('backup');
            const visitResult = await this.promisifyRequest(visitRequest);

            if (!visitResult || !visitResult.data || !Array.isArray(visitResult.data)) {
                console.log('IndexedDB中没有找到备份数据');
                if (typeof showToast === 'function') {
                    showToast('没有找到可恢复的备份数据', 'warning');
                }
                db.close();
                return false;
            }

            // 确认恢复操作
            const backupTime = new Date(visitResult.timestamp).toLocaleString();
            const confirmed = confirm(`确定要从备份恢复数据吗？\n\n备份时间: ${backupTime}\n备份记录: ${visitResult.data.length} 条\n\n恢复后当前数据将被覆盖`);
            if (!confirmed) {
                db.close();
                return false;
            }

            // 恢复访问历史
            this.visitHistory = visitResult.data;
            this.saveVisitHistory();
            console.log(`从IndexedDB恢复访问历史: ${this.visitHistory.length} 条记录`);

            // 恢复其他设置
            const settingsStore = transaction.objectStore('settings');
            const settingsKeys = ['navsphere-theme', 'navSphere_viewMode', 'navsphere-current-category', 'navsphere-expanded-categories'];

            for (const key of settingsKeys) {
                try {
                    const settingRequest = settingsStore.get(key);
                    const settingResult = await this.promisifyRequest(settingRequest);
                    if (settingResult && settingResult.value) {
                        localStorage.setItem(key, settingResult.value);
                        console.log(`恢复设置: ${key}`);
                    }
                } catch (error) {
                    console.warn(`恢复设置失败: ${key}`, error);
                }
            }

            db.close();

            if (typeof showToast === 'function') {
                showToast('数据恢复成功！页面将刷新以应用设置', 'success');
                setTimeout(() => location.reload(), 2000);
            }

            return true;
        } catch (error) {
            console.error('从IndexedDB恢复失败:', error);
            if (typeof showToast === 'function') {
                showToast('数据恢复失败，请查看控制台了解详情', 'error');
            }
            return false;
        }
    }

    /**
     * 将IDBRequest转换为Promise
     * @param {IDBRequest} request IDB请求对象
     * @returns {Promise} Promise对象
     */
    promisifyRequest(request) {
        return new Promise((resolve, reject) => {
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    /**
     * 导出所有数据
     */
    exportData() {
        const exportData = {
            version: '1.0',
            timestamp: Date.now(),
            visitHistory: this.visitHistory,
            settings: {
                theme: localStorage.getItem('navsphere-theme'),
                viewMode: localStorage.getItem('navSphere_viewMode'),
                currentCategory: localStorage.getItem('navsphere-current-category'),
                expandedCategories: localStorage.getItem('navsphere-expanded-categories')
            }
        };

        const blob = new Blob([JSON.stringify(exportData, null, 2)], {
            type: 'application/json'
        });

        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `navsphere-backup-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        console.log('数据导出成功');
        if (typeof showToast === 'function') {
            showToast('数据导出成功！', 'success');
        }
    }

    /**
     * 导入数据
     * @param {File} file 导入的文件
     */
    importData(file) {
        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const data = JSON.parse(e.target.result);
                if (this.validateImportData(data)) {
                    // 备份当前数据
                    const currentData = {
                        visitHistory: this.visitHistory,
                        timestamp: Date.now()
                    };
                    localStorage.setItem('navsphere-import-backup', JSON.stringify(currentData));

                    // 导入新数据
                    this.visitHistory = data.visitHistory || [];
                    this.saveVisitHistory();

                    // 恢复其他设置
                    if (data.settings) {
                        Object.entries(data.settings).forEach(([key, value]) => {
                            if (value) {
                                localStorage.setItem(key, value);
                            }
                        });
                    }

                    // 备份到IndexedDB
                    this.backupToIndexedDB();

                    console.log('数据导入成功');
                    if (typeof showToast === 'function') {
                        showToast('数据导入成功！页面将刷新以应用新设置', 'success');
                        setTimeout(() => location.reload(), 2000);
                    }
                } else {
                    console.error('导入失败：数据格式不正确');
                    if (typeof showToast === 'function') {
                        showToast('导入失败：数据格式不正确', 'error');
                    }
                }
            } catch (error) {
                console.error('导入数据失败:', error);
                if (typeof showToast === 'function') {
                    showToast('导入失败：文件格式错误', 'error');
                }
            }
        };
        reader.readAsText(file);
    }

    /**
     * 验证导入数据格式
     * @param {Object} data 导入的数据
     * @returns {boolean} 数据是否有效
     */
    validateImportData(data) {
        if (!data || typeof data !== 'object') {
            console.error('数据不是有效的对象');
            return false;
        }

        if (!data.version) {
            console.error('缺少版本信息');
            return false;
        }

        if (!Array.isArray(data.visitHistory)) {
            console.error('访问历史不是数组');
            return false;
        }

        // 验证访问历史数据格式
        const isValidHistory = data.visitHistory.every(item => {
            const hasRequiredFields = item.siteId &&
                                    item.siteName &&
                                    typeof item.visitCount === 'number' &&
                                    typeof item.lastVisit === 'number';

            if (!hasRequiredFields) {
                console.error('访问历史项缺少必要字段:', item);
                return false;
            }

            return true;
        });

        if (!isValidHistory) {
            console.error('访问历史数据格式无效');
            return false;
        }

        console.log('数据验证通过');
        return true;
    }

    /**
     * 清除历史记录（增强版，包含确认和备份）
     */
    clearHistory() {
        if (this.visitHistory.length === 0) {
            console.log('访问历史已为空');
            if (typeof showToast === 'function') {
                showToast('访问历史已为空', 'info');
            }
            return;
        }

        // 确认对话框
        const confirmed = confirm(`确定要清除所有访问历史吗？\n\n当前有 ${this.visitHistory.length} 条记录\n清除后可以通过"恢复备份"功能恢复数据`);
        if (!confirmed) {
            return;
        }

        // 备份当前数据到临时存储
        const backupData = {
            visitHistory: [...this.visitHistory],
            timestamp: Date.now()
        };
        localStorage.setItem('navsphere-clear-backup', JSON.stringify(backupData));

        // 清除数据
        this.visitHistory = [];
        this.saveVisitHistory();

        // 刷新UI显示
        this.refreshUIAfterClear();

        console.log('访问历史已清除');
        if (typeof showToast === 'function') {
            showToast('访问历史已清除，可通过"恢复备份"恢复', 'success');
        }
    }

    /**
     * 清除历史后刷新UI显示
     */
    refreshUIAfterClear() {
        // 获取全局应用实例
        const app = window.navApp;
        if (!app) return;

        // 如果当前正在显示常用分类，需要刷新显示
        if (app.currentCategory === 'frequent') {
            // 刷新常用分类的显示（显示空状态）
            app.renderSites('frequent');
        }

        // 刷新侧边栏的常用分类计数
        if (app.sidebarManager) {
            app.sidebarManager.updateFrequentCount();
        }
    }

    /**
     * 恢复历史后刷新UI显示
     */
    refreshUIAfterRestore() {
        // 获取全局应用实例
        const app = window.navApp;
        if (!app) return;

        // 如果当前正在显示常用分类，需要刷新显示
        if (app.currentCategory === 'frequent') {
            // 刷新常用分类的显示（显示恢复的网站）
            app.renderSites('frequent');
        }

        // 刷新侧边栏的常用分类计数
        if (app.sidebarManager) {
            app.sidebarManager.updateFrequentCount();
        }
    }

    /**
     * 恢复最近清除的历史记录
     */
    restoreLastClear() {
        try {
            const backupData = localStorage.getItem('navsphere-clear-backup');
            if (backupData) {
                const data = JSON.parse(backupData);
                if (data.visitHistory && Array.isArray(data.visitHistory)) {
                    this.visitHistory = data.visitHistory;
                    this.saveVisitHistory();
                    localStorage.removeItem('navsphere-clear-backup');

                    // 刷新UI显示
                    this.refreshUIAfterRestore();

                    console.log('历史记录已恢复');
                    if (typeof showToast === 'function') {
                        showToast('历史记录已恢复', 'success');
                    }
                    return true;
                }
            }

            console.log('没有找到可恢复的历史记录');
            if (typeof showToast === 'function') {
                showToast('没有找到可恢复的历史记录', 'info');
            }
            return false;
        } catch (error) {
            console.error('恢复历史记录失败:', error);
            if (typeof showToast === 'function') {
                showToast('恢复历史记录失败', 'error');
            }
            return false;
        }
    }

    /**
     * 调度备份（防抖处理）
     */
    scheduleBackup() {
        // 清除之前的定时器
        if (this.backupTimer) {
            clearTimeout(this.backupTimer);
        }

        // 设置新的定时器，5秒后执行备份
        this.backupTimer = setTimeout(() => {
            this.backupToIndexedDB();
            this.backupTimer = null;
        }, 5000);
    }

    /**
     * 获取数据健康状态
     * @returns {Object} 数据健康状态信息
     */
    async getDataHealthStatus() {
        const status = {
            localStorage: this.checkDataHealth(),
            visitHistoryCount: this.visitHistory.length,
            indexedDBAvailable: false,
            lastBackupTime: null,
            hasBackupData: false
        };

        try {
            const db = await this.openIndexedDB();
            status.indexedDBAvailable = true;

            const transaction = db.transaction(['visits'], 'readonly');
            const visitStore = transaction.objectStore('visits');
            const request = visitStore.get('backup');
            const result = await this.promisifyRequest(request);

            if (result && result.timestamp) {
                status.lastBackupTime = new Date(result.timestamp);
                status.hasBackupData = true;
            }

            db.close();
        } catch (error) {
            console.error('检查IndexedDB状态失败:', error);
        }

        return status;
    }

    /**
     * 显示数据状态信息
     */
    async showDataStatus() {
        const status = await this.getDataHealthStatus();
        this.showDataStatusCard(status);
        return status;
    }

    /**
     * 显示数据状态卡片
     * @param {Object} status 数据状态信息
     */
    showDataStatusCard(status) {
        // 移除已存在的数据状态卡片
        const existingOverlay = document.getElementById('dataStatusOverlay');
        if (existingOverlay) {
            existingOverlay.remove();
        }

        // 计算备份时间显示
        let backupTimeText = '无备份记录';
        if (status.hasBackupData && status.lastBackupTime) {
            const timeDiff = Date.now() - status.lastBackupTime.getTime();
            const hours = Math.floor(timeDiff / (1000 * 60 * 60));
            backupTimeText = hours < 1 ? '不到1小时前' : `${hours}小时前`;
        }

        // 确定整体状态
        const overallStatus = status.localStorage && status.indexedDBAvailable ? 'healthy' : 'warning';
        const statusIcon = overallStatus === 'healthy' ? 'fa-check-circle' : 'fa-exclamation-triangle';
        const statusText = overallStatus === 'healthy' ? '数据状态正常' : '数据状态异常';
        const statusClass = overallStatus === 'healthy' ? 'status-healthy' : 'status-warning';

        // 创建数据状态卡片HTML（包含背景遮罩）
        const cardHTML = `
            <div id="dataStatusOverlay" class="data-status-overlay">
                <div id="dataStatusCard" class="data-status-card ${statusClass}">
                    <div class="data-status-header">
                        <div class="status-icon">
                            <i class="fas ${statusIcon}"></i>
                        </div>
                        <div class="status-title">
                            <h3>${statusText}</h3>
                            <p>数据健康状态报告</p>
                        </div>
                        <button class="close-btn" onclick="document.getElementById('dataStatusOverlay').remove()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="data-status-body">
                        <div class="status-item">
                            <div class="status-item-icon">
                                <i class="fas fa-database"></i>
                            </div>
                            <div class="status-item-content">
                                <span class="status-item-label">本地存储</span>
                                <span class="status-item-value ${status.localStorage ? 'status-ok' : 'status-error'}">
                                    ${status.localStorage ? '✅ 正常' : '❌ 异常'}
                                </span>
                            </div>
                        </div>
                        <div class="status-item">
                            <div class="status-item-icon">
                                <i class="fas fa-history"></i>
                            </div>
                            <div class="status-item-content">
                                <span class="status-item-label">访问历史</span>
                                <span class="status-item-value">${status.visitHistoryCount} 条记录</span>
                            </div>
                        </div>
                        <div class="status-item">
                            <div class="status-item-icon">
                                <i class="fas fa-server"></i>
                            </div>
                            <div class="status-item-content">
                                <span class="status-item-label">IndexedDB</span>
                                <span class="status-item-value ${status.indexedDBAvailable ? 'status-ok' : 'status-error'}">
                                    ${status.indexedDBAvailable ? '✅ 可用' : '❌ 不可用'}
                                </span>
                            </div>
                        </div>
                        <div class="status-item">
                            <div class="status-item-icon">
                                <i class="fas fa-save"></i>
                            </div>
                            <div class="status-item-content">
                                <span class="status-item-label">最后备份</span>
                                <span class="status-item-value">${backupTimeText}</span>
                            </div>
                        </div>
                    </div>
                    <div class="data-status-footer">
                        <small class="text-muted">点击右上角 × 或点击外部区域关闭此卡片</small>
                    </div>
                </div>
            </div>
        `;

        // 将卡片添加到页面
        document.body.insertAdjacentHTML('beforeend', cardHTML);

        // 获取遮罩和卡片元素
        const overlay = document.getElementById('dataStatusOverlay');
        const card = document.getElementById('dataStatusCard');

        // 添加点击外部区域关闭功能
        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                overlay.remove();
            }
        });

        // 添加ESC键关闭功能
        const handleEscKey = (e) => {
            if (e.key === 'Escape') {
                overlay.remove();
                document.removeEventListener('keydown', handleEscKey);
            }
        };
        document.addEventListener('keydown', handleEscKey);

        // 添加显示动画
        setTimeout(() => {
            card.classList.add('show');
        }, 10);

        // 5秒后自动淡出提示
        setTimeout(() => {
            const footer = card.querySelector('.data-status-footer');
            if (footer) {
                footer.style.opacity = '0.5';
            }
        }, 5000);
    }
}
